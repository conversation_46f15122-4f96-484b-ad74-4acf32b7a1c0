{"name": "user-center", "version": "1.0.0", "description": "User Center for Chrome Extension", "scripts": {"build": "cross-env NODE_ENV=production webpack --mode production", "build:fast": "cross-env NODE_ENV=production BUILD_MODE=fast webpack --mode production", "dev": "cross-env NODE_ENV=development webpack --mode development --watch", "dev:server": "cross-env NODE_ENV=development webpack serve --mode development", "watch": "cross-env NODE_ENV=development webpack --mode development --watch"}, "dependencies": {"element-plus": "^2.1.0", "vue": "^3.2.31", "vue-router": "^4.5.1"}, "devDependencies": {"@babel/core": "^7.17.5", "@babel/preset-env": "^7.16.11", "@types/chrome": "^0.0.246", "@types/copy-webpack-plugin": "^8.0.1", "@types/node": "^18.11.9", "@types/webpack-dev-server": "^4.7.2", "@vue/compiler-sfc": "^3.2.31", "babel-loader": "^8.2.3", "cross-env": "^7.0.3", "css-loader": "^6.7.1", "esbuild-loader": "^4.3.0", "html-webpack-plugin": "^5.5.0", "style-loader": "^3.3.1", "terser-webpack-plugin": "^5.3.14", "ts-loader": "^9.2.8", "typescript": "^4.6.2", "vue-loader": "^17.0.0", "webpack": "^5.70.0", "webpack-cli": "^4.9.2"}}