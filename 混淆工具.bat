@echo off
chcp 65001 >nul
echo.
echo ==========================================
echo    TSA Chrome扩展代码混淆工具 v1.0
echo ==========================================
echo.

echo 🚀 启动混淆工具...
echo.

:: 检查plugin/gulp目录是否存在
if not exist "plugin\gulp\obfuscate.bat" (
    echo ❌ 错误: 混淆工具未找到
    echo 请确保 plugin/gulp/obfuscate.bat 文件存在
    pause
    exit /b 1
)

:: 切换到plugin/gulp目录并运行混淆工具
cd plugin\gulp
call obfuscate.bat

:: 返回原目录
cd ..\..

echo.
echo 👋 混淆工具已退出
pause 