<?php

namespace App\Models\N11;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Builder;

class N11RejectedProductModel extends BaseModel
{
    /**
     * 表名
     */
    protected $table = 'n11_rejected_products';

    /**
     * 可批量赋值的字段
     */
    protected $fillable = [
        'product_id',
        'user_id',
        'sku_id',
        'title',
        'commission',
        'image_url',
        'brand',
        'commission_rate_value',
        'stock_code',
        'barcode',
        'product_main_id',
        'sales_price',
        'list_price',
        'quantity',
        'status_original',
        'group_id',
        'category_name',
        'preparing_time',
        'catalog_id',
        'shipment_template',
        'in_approval_reason',
        'reject_info',
        'vat_rate',
        'status'
    ];

    /**
     * 字段类型转换
     */
    protected $casts = [
        'commission' => 'decimal:2',
        'commission_rate_value' => 'decimal:2',
        'sales_price' => 'decimal:2',
        'list_price' => 'decimal:2',
        'vat_rate' => 'decimal:2',
        'quantity' => 'integer',
        'preparing_time' => 'integer',
        'status' => 'integer',
        'in_approval_reason' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 按用户ID筛选
     */
    public function scopeByUserId(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 筛选待处理记录（status=0）
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 0);
    }

    /**
     * 筛选已完成记录（status=1）
     */
    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('status', 1);
    }

    /**
     * 获取审批原因访问器
     */
    public function getInApprovalReasonAttribute($value)
    {
        if (is_string($value)) {
            return json_decode($value, true) ?: [];
        }
        return $value ?: [];
    }

    /**
     * 设置审批原因修改器
     */
    public function setInApprovalReasonAttribute($value)
    {
        $this->attributes['in_approval_reason'] = is_array($value) ? json_encode($value) : $value;
    }
} 