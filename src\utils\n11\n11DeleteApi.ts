// 导入Chrome扩展消息发送函数
declare const chrome: any;

/**
 * 通过background页面发送请求
 */
function sendRequestViaBackground(url: string, options: any): Promise<any> {
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({
      funType: 'axios',
      url: url,
      method: options.method || 'GET',
      pramas: options.body ? JSON.parse(options.body) : {},
      headers: options.headers || {},
      auth: options.auth || false,
      encrypto: options.encrypto || false,
      timeout: 15000
    }, (response: any) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
        return;
      }
      
      if (response && response[0]) {
        const result = response[0];
        if (result.status >= 200 && result.status < 300) {
          resolve({ success: true, data: result.data });
        } else {
          reject(new Error(`请求失败: ${result.status}`));
        }
      } else {
        reject(new Error('请求未返回有效数据'));
      }
    });
  });
}

// 删除响应数据类型
export interface N11DeleteResponse {
  successProductIds: string[]
  failProductIds: string[]
  systemErrorProductIds: string[]
  message?: string
}

/**
 * 批量删除N11商品
 * @param productIds 商品ID数组
 * @returns 删除结果
 */
export async function deleteN11ProductsBulk(productIds: string[]): Promise<N11DeleteResponse> {
  try {
    const options = {
      method: 'DELETE',
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'Origin': 'https://so.n11.com',
        'Referer': 'https://so.n11.com/magaza/urun-listesi',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      },
      body: JSON.stringify({ productIds }),
      auth: false,
      encrypto: false
    }

    const response = await sendRequestViaBackground(
      'https://so.n11.com/api/endpoint-products/delete-bulk',
      options
    )

    if (response.success && response.data) {
      return response.data
    } else {
      throw new Error('删除商品失败')
    }
  } catch (error) {
    console.error('批量删除N11商品失败:', error)
    throw error
  }
}

/**
 * 删除单个N11商品
 * @param productId 商品ID
 * @returns 删除结果
 */
export async function deleteSingleN11Product(productId: string): Promise<N11DeleteResponse> {
  try {
    return await deleteN11ProductsBulk([productId])
  } catch (error) {
    console.error('删除单个N11商品失败:', error)
    throw error
  }
}

/**
 * 检查删除结果
 * @param response 删除响应
 * @param productId 商品ID
 * @returns 是否删除成功
 */
export function isDeleteSuccess(response: N11DeleteResponse, productId: string): boolean {
  return response.successProductIds.includes(productId)
}

/**
 * 获取删除失败的原因
 * @param response 删除响应
 * @param productId 商品ID
 * @returns 失败原因
 */
export function getDeleteFailureReason(response: N11DeleteResponse, productId: string): string {
  if (response.failProductIds.includes(productId)) {
    return '删除失败'
  } else if (response.systemErrorProductIds.includes(productId)) {
    return '系统错误'
  } else {
    return '未知错误'
  }
} 