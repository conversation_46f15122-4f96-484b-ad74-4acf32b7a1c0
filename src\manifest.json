{"manifest_version": 3, "name": "跨境蜂", "version": "1.0.0", "description": "智能商品抓取与多类目裂变，同步支持Temu商品批量速达多外贸平台，一键跨平台铺货，高效拓展全球市场。", "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "icons": {"16": "static/img/icon.png", "19": "static/img/icon.png", "38": "static/img/icon.png", "48": "static/img/icon.png", "128": "static/img/icon.png"}, "action": {"default_icon": "static/img/icon.png", "default_title": "跨境蜂", "default_popup": "popup.html"}, "options_page": "option.html", "background": {"service_worker": "background.main.js"}, "web_accessible_resources": [{"resources": ["web/*"], "matches": ["<all_urls>"]}], "content_scripts": [{"matches": ["*://www.temu.com/*"], "css": [], "js": ["temu_detail.main.js"], "run_at": "document_end"}, {"matches": ["*://so.n11.com/selleroffice/v2/product/products*"], "css": [], "js": ["n11_product_list.main.js"], "run_at": "document_end"}], "host_permissions": ["*://*/*"], "permissions": ["declarativeNetRequest", "declarativeNetRequestWithHostAccess", "declarativeNetRequestFeedback", "contextMenus", "tabs", "debugger", "activeTab", "notifications", "storage", "unlimitedStorage", "downloads", "cookies", "management", "webNavigation", "scripting", "windows"]}