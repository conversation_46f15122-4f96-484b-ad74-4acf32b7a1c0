<?php
declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Wap\TeMu;

use App\Http\Controllers\Api\Controller;
use App\Service\TeMu\ProductCategoryService;

class ProductCategoryController extends Controller
{
    protected ProductCategoryService $productCategoryService;

    public function __construct(ProductCategoryService $productCategoryService)
    {
        $this->productCategoryService = $productCategoryService;
        parent::__construct();
    }

    /**
     * 采集Temu商品分类 
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function collectCategories()
    {
        // 非命令行模式调用
        $result = $this->productCategoryService->collectCategories(false);

        if ($result['status'] == 1) {
            return $this->apiSuccess($result, 200, $result['message']);
        } else {
            return $this->apiError($result['message'], 400, $result);
        }
    }

    /**
     * 从网页JSON文件采集Temu商品分类
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function collectWebPageCategories()
    {
        // 非命令行模式调用
        $result = $this->productCategoryService->collectCategoriesFromWebPage(false);

        if ($result['status'] == 1) {
            return $this->apiSuccess($result, 200, $result['message']);
        } else {
            return $this->apiError($result['message'], 400, $result);
        }
    }
}
