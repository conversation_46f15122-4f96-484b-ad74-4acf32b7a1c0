# 商品图片本地化功能

## 功能概述

商品采集完成后，系统会自动将商品的所有图片和视频下载到本地服务器，并更新数据库中的URL为本地路径。这样可以避免外部图片链接失效的问题，提高系统稳定性。

## 功能特点

1. **分批处理**：每次最多处理5张图片，避免内存溢出和超时
2. **进度显示**：前端实时显示处理进度
3. **错误重试**：图片下载失败会自动重试
4. **智能优化**：下载时进行图片优化，但保持原始文件名
5. **目录管理**：按用户和商品ID分类存储
6. **批量处理**：支持批量检查和处理目录下所有商品的图片

## 处理流程

### Chrome扩展流程（src目录）
1. 商品采集成功后，不直接显示成功提示
2. 调用图片处理接口获取处理信息
3. 循环调用处理接口直到完成
4. 显示最终的成功提示（包含图片和视频数量）

### Web管理界面流程（web目录）
1. 在商品目录页面点击"检查商品图片"按钮
2. 获取当前目录下所有需要处理图片的商品ID列表
3. 批量处理每个商品的图片，显示详细进度
4. 发布商品前自动检查图片处理状态
5. 提供跳过图片处理的选项

### 后端流程
1. 获取商品的所有媒体文件信息
2. 分批下载图片（每批5张）
3. 处理视频文件（单独处理）
4. 更新数据库中的URL
5. 标记处理完成状态

## 存储结构

```
public/attachment/
├── {user_id}/
│   ├── {goods_id}/
│   │   ├── goods_pic/          # 商品主图
│   │   ├── sku/                # SKU图片
│   │   ├── goods_video/        # 商品视频
│   │   └── goods_instruction_images/  # 介绍图片
```

## API接口

### 处理商品图片
- **URL**: `/api/user/process-goods-images`
- **方法**: POST
- **参数**:
  - `goods_id`: 商品ID
  - `process_step`: 处理步骤（从1开始）

### 获取需要处理图片的商品列表
- **URL**: `/api/goods/need-image-process`
- **方法**: GET
- **参数**:
  - `directory_id`: 目录ID

### 响应格式

#### 图片处理响应
```json
{
    "code": 200,
    "data": {
        "goods_id": 1,
        "img_local_status": 0,
        "total_images": 15,
        "processed_images": 5,
        "total_videos": 1,
        "processed_videos": 0,
        "current_batch_processed": 5,
        "is_completed": false,
        "directory_name": "默认目录"
    }
}
```

#### 需要处理图片的商品列表响应
```json
{
    "code": 200,
    "data": {
        "goods_ids": [1, 2, 3, 4, 5],
        "total_count": 5
    }
}
```

## 数据库字段

### tsa_user_goods表
- `img_local_status`: 图片本地化状态（0-未完成，1-已完成）

### 图片URL格式
- **远程URL**: `https://img.kwcdn.com/product/fancy/xxx.jpg`
- **下载URL**: `https://img.kwcdn.com/product/fancy/xxx.jpg?imageView2/2/w/602/q/90/format/webp`
- **本地URL**: `attachment/1/123/goods_pic/xxx.jpg`（保持原始扩展名）

## 错误处理

1. **下载失败**: 自动重试2次，仍失败则移除该图片
2. **超时处理**: 图片下载30秒超时，视频120秒超时
3. **目录创建**: 自动创建不存在的目录
4. **文件存在**: 跳过已存在的文件，避免重复下载
5. **空数组处理**: 当所有图片下载失败时，存储空字符串而不是空数组JSON

## 配置说明

### 常量配置
- `BATCH_SIZE`: 每批处理图片数量（默认5张）
- `DOWNLOAD_TIMEOUT`: 图片下载超时时间（默认30秒）
- `RETRY_TIMES`: 重试次数（默认2次）

### 图片处理参数
图片下载时会添加处理参数进行优化，但保存时使用原始文件名：
- 下载URL添加参数：`?imageView2/2/w/602/q/90/format/webp`
- 宽度限制602px，质量90%，转换为webp格式
- 保存文件名保持原始扩展名（如原图是.jpg，保存也是.jpg）

## 使用场景

### Chrome扩展场景
- 商品采集后自动触发图片处理
- 适用于单个商品的实时处理
- 提供详细的处理进度反馈

### Web管理界面场景
- 批量检查目录下所有商品的图片状态
- 批量处理多个商品的图片
- 发布商品前的图片状态检查
- 提供跳过处理的灵活选项

## 进度显示

### 单商品处理进度
- 显示图片和视频的总数量
- 实时显示已处理的数量和百分比
- 显示当前处理步骤

### 批量处理进度
- 显示总商品数量
- 显示当前处理的商品序号和ID
- 显示剩余商品数量
- 嵌套显示单个商品的处理进度

## 注意事项

1. 确保`public/attachment`目录有写权限
2. 服务器需要足够的存储空间
3. 网络环境要稳定，避免下载失败
4. 大量图片处理时注意服务器性能
5. 批量处理时避免同时处理过多商品
6. SSL证书问题已通过`withoutVerifying()`解决

## 故障排除

### 常见问题
1. **权限错误**: 检查目录写权限
2. **网络超时**: 调整超时时间配置
3. **存储空间不足**: 清理旧文件或扩容
4. **内存不足**: 减少批处理数量
5. **SSL证书错误**: 已通过跳过验证解决

### 日志查看
系统会记录详细的处理日志，可通过Laravel日志查看：
```bash
tail -f storage/logs/laravel.log
```

## 性能优化建议

1. **异步处理**: 考虑将批量处理改为异步任务队列
2. **并行处理**: 可以考虑同时处理多个商品（需注意服务器负载）
3. **进度持久化**: 将处理进度保存到数据库，支持断点续传
4. **缓存机制**: 对已处理的图片建立缓存机制
5. **分时处理**: 在服务器负载较低时进行批量处理 