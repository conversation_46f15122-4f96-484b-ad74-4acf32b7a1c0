# 商品存储逻辑调整说明

## 主要修改内容

### 1. 商品存在性验证逻辑调整

#### 修改前
```php
$goodsModel = GoodsModel::where([
    'user_id' => $data['user_id'], 
    'goods_id' => $goods_id, 
    'type' => $type
])->first();
```

#### 修改后
```php
$goodsModel = GoodsModel::where([
    'user_id' => $data['user_id'], 
    'goods_id' => $goods_id, 
    'type' => $type,
    'directory_id' => $data['directory_id'] ?? 0  // 新增目录ID条件
])->first();
```

**说明**：现在每个用户不同的 `directory_id` 可以存储相同的商品 `goods_id`。

### 2. 目录商品数量统计方法

#### A. GoodsService.php 新增方法

```php
/**
 * 更新目录商品数量统计
 * 使用统计计算的方法更新值，不使用increment/decrement
 */
private function updateDirectoryGoodsCount(int $userId, int $newDirectoryId, ?int $oldDirectoryId, bool $isNewGoods): void
{
    // 智能判断需要更新的目录
    // 新商品：只更新新目录
    // 更新商品且目录变化：更新新旧两个目录
    // 更新商品且目录未变化：不更新
}

/**
 * 重新计算指定目录的商品数量
 */
private function recalculateDirectoryGoodsCount(int $userId, int $directoryId): void
{
    // 统计该目录下的有效商品数量
    $goodsCount = GoodsModel::where('user_id', $userId)
        ->where('directory_id', $directoryId)
        ->where('status', 1)
        ->count();

    // 更新目录的商品数量
    UserGoodsDirectoryModel::where('user_id', $userId)
        ->where('id', $directoryId)
        ->update(['goods_count' => $goodsCount]);
}
```

#### B. UserGoodsDirectoryModel.php 新增方法

```php
/**
 * 批量更新指定用户所有目录的商品数量
 * 使用统计计算的方法，不使用increment/decrement
 */
public static function updateAllGoodsCountByUser(int $userId): void
{
    // 获取用户所有目录，逐个统计并更新
}

/**
 * 重新计算单个目录的商品数量
 */
public static function recalculateGoodsCount(int $userId, int $directoryId): void
{
    // 统计并更新指定目录的商品数量
}
```

#### C. UserGoodsDirectoryService.php 方法更新

```php
/**
 * 更新目录商品数量
 * 使用统计计算的方法更新值，不使用increment/decrement
 */
public function updateDirectoryGoodsCount(int $directoryId): void
{
    // 直接统计计算，不使用模型的updateGoodsCount方法
}

/**
 * 批量更新所有目录的商品数量
 * 使用统计计算的方法更新值，不使用increment/decrement
 */
public function updateAllDirectoryGoodsCount(int $userId): void
{
    // 调用模型的批量更新方法
}
```

### 3. 商品保存流程优化

#### 新的保存流程
1. **验证商品是否存在**：增加 `directory_id` 条件
2. **保存/更新商品数据**：记录旧目录ID和是否为新商品
3. **处理SKU数据**：保持原有逻辑
4. **更新目录商品数量**：智能更新相关目录的商品统计

#### 智能更新逻辑
- **新商品**：只更新新目录的商品数量
- **更新商品且目录变化**：更新新旧两个目录的商品数量
- **更新商品且目录未变化**：不需要更新商品数量

### 4. API接口

现有的API接口保持不变：
- `POST /api/goods_directory/update-goods-count` - 更新单个目录商品数量
- `POST /api/goods_directory/update-all-goods-count` - 批量更新所有目录商品数量

### 5. 技术特点

#### 优势
1. **数据一致性**：使用统计计算确保数据准确性
2. **支持重复商品**：同一商品可以存储在不同目录中
3. **智能更新**：只在必要时更新目录统计
4. **性能优化**：避免频繁的increment/decrement操作

#### 注意事项
1. 所有商品数量统计都基于 `status = 1` 的有效商品
2. 目录ID为0表示未分类商品
3. 更新操作在事务中执行，确保数据一致性

## 测试建议

### 测试场景
1. **新商品保存**：验证目录商品数量正确增加
2. **商品更新（同目录）**：验证商品数量不变
3. **商品更新（换目录）**：验证新旧目录商品数量都正确更新
4. **重复商品保存**：验证同一商品可以保存到不同目录
5. **批量更新统计**：验证所有目录商品数量统计正确

### 验证方法
```sql
-- 验证目录商品数量统计是否正确
SELECT 
    d.id,
    d.name,
    d.goods_count as recorded_count,
    COUNT(g.id) as actual_count
FROM user_goods_directory d
LEFT JOIN user_goods g ON d.id = g.directory_id AND g.status = 1
WHERE d.user_id = ?
GROUP BY d.id, d.name, d.goods_count
HAVING recorded_count != actual_count;
```

如果查询结果为空，说明统计数据正确。 