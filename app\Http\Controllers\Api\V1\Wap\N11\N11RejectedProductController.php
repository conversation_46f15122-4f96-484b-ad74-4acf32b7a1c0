<?php

namespace App\Http\Controllers\Api\V1\Wap\N11;

use App\Http\Controllers\Api\Controller;
use App\Service\N11\N11RejectedProductService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\Validator;

class N11RejectedProductController extends Controller
{
    protected $n11RejectedProductService;

    public function __construct(N11RejectedProductService $n11RejectedProductService)
    {
        $this->n11RejectedProductService = $n11RejectedProductService;
    }

    /**
     * 批量保存被拒绝商品
     */
    public function batchSave(Request $request): JsonResponse
    {
        try {
            $user = $request->attributes->get('user');
            $userId = $user['id'];

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'products' => 'required|array|min:1',
                'products.*.product_id' => 'required|string'
            ]);

            if ($validator->fails()) {
                return $this->apiError('参数验证失败: ' . $validator->errors()->first());
            }

            $products = $request->input('products');
            $result = $this->n11RejectedProductService->batchSaveRejectedProducts($userId, $products);

            return $this->apiSuccess($result);

        } catch (MyException $e) {
            return $this->apiError($e->getMessage());
        } catch (\Exception $e) {
            return $this->apiError('系统异常: ' . $e->getMessage());
        }
    }

    /**
     * 获取待处理商品数量
     */
    public function getPendingCount(Request $request): JsonResponse
    {
        try {
            $user = $request->attributes->get('user');
            $userId = $user['id'];

            $count = $this->n11RejectedProductService->getPendingCount($userId);

            return $this->apiSuccess(['pending_count' => $count]);

        } catch (MyException $e) {
            return $this->apiError($e->getMessage());
        } catch (\Exception $e) {
            return $this->apiError('系统异常: ' . $e->getMessage());
        }
    }

    /**
     * 获取下一个待处理商品
     */
    public function getNextPending(Request $request): JsonResponse
    {
        try {
            $user = $request->attributes->get('user');
            $userId = $user['id'];

            $product = $this->n11RejectedProductService->getNextPendingProduct($userId);

            if (!$product) {
                return $this->apiSuccess(['product' => null, 'message' => '没有待处理的商品']);
            }

            return $this->apiSuccess(['product' => $product]);

        } catch (MyException $e) {
            return $this->apiError($e->getMessage());
        } catch (\Exception $e) {
            return $this->apiError('系统异常: ' . $e->getMessage());
        }
    }

    /**
     * 更新商品处理状态
     */
    public function updateStatus(Request $request): JsonResponse
    {
        try {
            $user = $request->attributes->get('user');
            $userId = $user['id'];

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'product_id' => 'required|string',
                'status' => 'required|integer|in:0,1'
            ]);

            if ($validator->fails()) {
                return $this->apiError('参数验证失败: ' . $validator->errors()->first());
            }

            $productId = $request->input('product_id');
            $status = $request->input('status');

            $result = $this->n11RejectedProductService->updateProductStatus($userId, $productId, $status);

            if ($result) {
                return $this->apiSuccess(['message' => '状态更新成功']);
            } else {
                return $this->apiError('状态更新失败，未找到对应记录');
            }

        } catch (MyException $e) {
            return $this->apiError($e->getMessage());
        } catch (\Exception $e) {
            return $this->apiError('系统异常: ' . $e->getMessage());
        }
    }

    /**
     * 获取处理统计信息
     */
    public function getStatistics(Request $request): JsonResponse
    {
        try {
            $user = $request->attributes->get('user');
            $userId = $user['id'];

            $statistics = $this->n11RejectedProductService->getProcessingStatistics($userId);

            return $this->apiSuccess($statistics);

        } catch (MyException $e) {
            return $this->apiError($e->getMessage());
        } catch (\Exception $e) {
            return $this->apiError('系统异常: ' . $e->getMessage());
        }
    }
} 