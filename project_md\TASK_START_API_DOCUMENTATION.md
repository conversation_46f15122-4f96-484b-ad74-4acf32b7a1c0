# 任务开启API文档

## 接口概述

任务开启API用于处理用户点击"开启"按钮时的业务逻辑，包括生成任务详情记录并返回第一条待处理的任务详情。

## 接口信息

- **接口地址**: `POST /api/user/task/start`
- **请求方式**: POST
- **认证方式**: 需要用户登录认证（userLogin中间件）

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| task_id | integer | 是 | 任务ID |

### 请求示例

```json
{
    "task_id": 1
}
```

## 响应参数

### 成功响应

```json
{
    "code": 200,
    "message": "任务开启成功",
    "data": {
        "id": 1,
        "user_id": 1,
        "task_id": 1,
        "user_account_id": 1,
        "user_goods_id": 1,
        "user_goods_sku_id": 1,
        "goods_id": *********,
        "thumb_url": "https://example.com/thumb.jpg",
        "currentcy": "USD",
        "price": "19.99",
        "spec_key_values": "颜色:红色;尺寸:L",
        "spec_values": "红色,L",
        "is_skc_gallery": 0,
        "category_id": null,
        "product_main_id": "",
        "stock_code": "",
        "status": 0,
        "goods_info": {
            "id": 1,
            "goods_name": "商品名称",
            "goods_pic": "[\"pic1.jpg\", \"pic2.jpg\"]",
            "source_url": "https://temu.com/product/123"
        },
        "sku_info": {
            "id": 1,
            "sku_id": *********,
            "url": "https://temu.com/sku/*********",
            "skc_gallery": "[\"gallery1.jpg\", \"gallery2.jpg\"]"
        },
        "store_info": {
            "id": 1,
            "account_name": "店铺名称",
            "account_logo": "https://example.com/logo.jpg"
        },
        "created_at": "2025-01-01 10:00:00",
        "updated_at": "2025-01-01 10:00:00"
    }
}
```

### 错误响应

```json
{
    "code": 400,
    "message": "任务ID参数无效",
    "data": null
}
```

```json
{
    "code": 400,
    "message": "任务不存在或无权限访问",
    "data": null
}
```

```json
{
    "code": 400,
    "message": "任务已完成，无需重复开启",
    "data": null
}
```

## 业务逻辑说明

### 1. 任务验证
- 验证任务ID是否有效
- 验证任务是否属于当前用户
- 验证任务是否已完成

### 2. 任务详情生成
如果任务没有待处理的详情记录，系统会自动生成：

#### 2.1 指定商品发布（is_selected = 1）
- 从任务的 `selected_ids` JSON数组中获取商品ID
- 查询这些指定商品的所有SKU
- 为每个SKU创建一条任务详情记录

#### 2.2 时间范围发布（is_selected = 0）
- 根据任务的 `day_start` 和 `day_end` 查询商品
- 按照 `sort_order`（asc/desc）排序
- 限制最多500条商品
- 为每个商品的所有SKU创建任务详情记录

### 3. 返回数据
- 返回按ID升序排列的第一条任务详情记录
- 包含商品信息、SKU信息、店铺信息等关联数据

## 数据库表说明

### user_task 表
存储任务基本信息，包括执行类型、时间范围、排序规则等。

### user_task_detail 表
存储任务的具体执行详情，每个SKU对应一条记录。

### 状态说明
- `status = 0`: 等待上传
- `status = 1`: 已成功上传
- `status = 2`: 已上传待审核
- `status = 3`: 上传失败

## 注意事项

1. 每次最多处理500个商品
2. 如果商品没有SKU，会创建一条默认记录
3. 任务完成后不能重复开启
4. 系统会自动更新任务的总数量（task_count）
5. 接口具有幂等性，重复调用不会重复生成记录 