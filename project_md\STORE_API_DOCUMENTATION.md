# 店铺管理 API 文档

## 概述
本文档描述了店铺管理相关的API接口，用于管理用户的店铺账户信息。所有接口都需要用户登录认证。

## 基础信息
- 基础URL: `http://tsa.test.com/api`
- 认证方式: 需要通过 `userLogin` 中间件验证
- 数据表: `tsa_user_account`
- 模型: `UserAccountModel`

## API 接口列表

### 1. 获取店铺列表
**接口地址:** `GET /store/list`

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | integer | 否 | 页码，默认1 |
| page_size | integer | 否 | 每页数量，默认10，最大100 |
| account_type | integer | 否 | 账户类型筛选：1=Temu, 2=N11 |
| keyword | string | 否 | 关键词搜索（账户名称、账户ID、品牌） |

**响应示例:**
```json
{
    "status": 200,
    "code": 1,
    "data": {
        "list": [
            {
                "id": 1,
                "account_type": 1,
                "account_type_name": "Temu",
                "account_id": "shop123",
                "account_name": "我的店铺",
                "account_logo": "https://example.com/logo.jpg",
                "brand": "品牌名称",
                "price_rate": 10,
                "cookie_status": true,
                "cookie_time": "2024-01-01 12:00:00",
                "created_at": "2024-01-01 10:00:00",
                "updated_at": "2024-01-01 12:00:00"
            }
        ],
        "pagination": {
            "current_page": 1,
            "page_size": 10,
            "total": 1,
            "total_pages": 1,
            "has_next": false,
            "has_prev": false
        }
    },
    "msg": "success"
}
```

### 2. 创建店铺
**接口地址:** `POST /store/create`

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account_type | integer | 是 | 账户类型：1=Temu, 2=N11 |
| account_id | string | 是 | 账户ID，最大100字符 |
| account_name | string | 是 | 账户名称，最大100字符 |
| account_logo | string | 否 | 账户Logo URL，最大255字符 |
| account_cookie | string | 否 | 账户Cookie |
| brand | string | 否 | 品牌名称，最大50字符 |
| price_rate | integer | 否 | 价格增长率百分比，0-1000，默认10 |
| app_key | string | 否 | 应用Key，最大255字符 |
| app_secret | string | 否 | 应用Secret，最大255字符 |

**响应示例:**
```json
{
    "status": 200,
    "code": 1,
    "data": {
        "id": 1,
        "message": "店铺创建成功"
    },
    "msg": "success"
}
```

### 3. 更新店铺
**接口地址:** `POST /store/update`

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 店铺ID |
| account_type | integer | 否 | 账户类型：1=Temu, 2=N11 |
| account_id | string | 否 | 账户ID，最大100字符 |
| account_name | string | 否 | 账户名称，最大100字符 |
| account_logo | string | 否 | 账户Logo URL，最大255字符 |
| account_cookie | string | 否 | 账户Cookie |
| brand | string | 否 | 品牌名称，最大50字符 |
| price_rate | integer | 否 | 价格增长率百分比，0-1000 |
| app_key | string | 否 | 应用Key，最大255字符 |
| app_secret | string | 否 | 应用Secret，最大255字符 |
| cookie_status | boolean | 否 | Cookie状态 |

**响应示例:**
```json
{
    "status": 200,
    "code": 1,
    "data": {
        "message": "店铺更新成功"
    },
    "msg": "success"
}
```

### 4. 删除店铺
**接口地址:** `POST /store/delete`

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 店铺ID |

**响应示例:**
```json
{
    "status": 200,
    "code": 1,
    "data": {
        "message": "店铺删除成功"
    },
    "msg": "success"
}
```

### 5. 获取店铺详情
**接口地址:** `GET /store/detail`

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 店铺ID |

**响应示例:**
```json
{
    "status": 200,
    "code": 1,
    "data": {
        "id": 1,
        "user_id": 1,
        "account_type": 1,
        "account_type_name": "Temu",
        "account_id": "shop123",
        "account_name": "我的店铺",
        "account_logo": "https://example.com/logo.jpg",
        "account_cookie": "cookie_string",
        "cookie_time": "2024-01-01 12:00:00",
        "cookie_status": true,
        "brand": "品牌名称",
        "price_rate": 10,
        "app_key": "app_key_string",
        "app_secret": "app_secret_string",
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-01-01 12:00:00"
    },
    "msg": "success"
}
```

## 错误响应
当发生错误时，API会返回相应的错误信息：

```json
{
    "status": 200,
    "code": 0,
    "msg": "错误信息描述"
}
```

常见错误：
- `店铺不存在` (404)
- `无权限操作此店铺` (403)
- `该账户已存在` (400)
- 验证错误信息

## 权限控制
- 所有接口都需要用户登录认证
- 用户只能操作自己的店铺信息
- 系统会自动验证店铺所有权，防止越权操作

## 数据库表结构
```sql
CREATE TABLE `tsa_user_account` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) unsigned DEFAULT '0',
  `account_type` smallint(3) DEFAULT '0' COMMENT '1 temu  2 n11',
  `account_id` varchar(100) DEFAULT '',
  `account_name` varchar(100) DEFAULT '',
  `account_logo` varchar(255) DEFAULT '',
  `account_cookie` text,
  `cookie_time` datetime DEFAULT NULL,
  `cookie_status` tinyint(1) unsigned DEFAULT '1',
  `brand` varchar(50) DEFAULT NULL COMMENT '品牌默认名称',
  `price_rate` int(10) unsigned DEFAULT '10' COMMENT '发布时默认价格增加的百分比值',
  `app_key` varchar(255) DEFAULT '',
  `app_secret` varchar(255) DEFAULT '',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4;
```

## 实现特性
1. **分页支持**: list接口支持完整的分页功能
2. **权限验证**: 所有操作都会验证用户权限
3. **数据验证**: 完整的请求参数验证
4. **搜索功能**: 支持关键词搜索和类型筛选
5. **错误处理**: 完善的错误处理和响应
6. **数据安全**: 防止重复创建和越权操作
7. **架构分层**: 采用Controller-Service-Model分层架构
8. **异常处理**: 统一的异常处理机制

## 技术架构
- **Controller层**: `StoreController` - 负责HTTP请求处理和响应
- **Service层**: `StoreService` - 负责业务逻辑处理
- **Model层**: `UserAccountModel` - 负责数据访问
- **异常处理**: `MyException` - 自定义异常类
- **数据验证**: Laravel Validator - 请求数据验证 