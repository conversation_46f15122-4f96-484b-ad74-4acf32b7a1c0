const { spawn, exec } = require('child_process');
const chokidar = require('chokidar');
const path = require('path');
const fs = require('fs');

console.log('🚀 启动开发监听模式...');
console.log('⚡ 使用快速构建模式：跳过代码压缩和类型检查，提升构建速度');
console.log('💡 提示：生产发布请使用 npm run b_tsa 进行完整构建');

let webBuildProcess = null;
let mainBuildProcess = null;
let isBuilding = false;

// 构建web项目
function buildWeb() {
  return new Promise((resolve, reject) => {
    console.log('📦 开始构建web项目（快速模式）...');
    
    // 进度提示
    let progressDots = 0;
    const progressInterval = setInterval(() => {
      progressDots = (progressDots + 1) % 4;
      const dots = '.'.repeat(progressDots);
      process.stdout.write(`\r🔄 正在构建中${dots}   `);
    }, 500);
    
    const buildProcess = spawn('npm', ['run', 'build:fast'], {
      cwd: path.join(__dirname, '../web'),
      stdio: ['inherit', 'pipe', 'pipe'],
      shell: true
    });

    // 监听构建输出以提供更详细的进度信息
    let buildOutput = '';
    buildProcess.stdout.on('data', (data) => {
      buildOutput += data.toString();
      // 检查是否有特定的构建阶段信息
      const output = data.toString();
      if (output.includes('building for production')) {
        process.stdout.write('\r📦 正在编译生产版本...     \n');
      } else if (output.includes('transforming')) {
        process.stdout.write('\r🔄 正在转换文件...       \n');
      } else if (output.includes('rendering chunks')) {
        process.stdout.write('\r📝 正在生成代码块...     \n');
      } else if (output.includes('computing gzip size')) {
        process.stdout.write('\r📊 正在计算文件大小...   \n');
      }
    });

    buildProcess.stderr.on('data', (data) => {
      // 清除进度提示，显示错误信息
      clearInterval(progressInterval);
      process.stdout.write('\r                    \r');
      console.error(data.toString());
    });

    buildProcess.on('close', (code) => {
      clearInterval(progressInterval);
      process.stdout.write('\r                    \r');
      
      if (code === 0) {
        console.log('✅ web项目构建完成（快速模式）');
        resolve();
      } else {
        console.error('❌ web项目构建失败');
        reject(new Error(`构建失败，退出码: ${code}`));
      }
    });
  });
}

// 复制web构建文件到主项目
function copyWebDist() {
  return new Promise((resolve, reject) => {
    console.log('📋 开始复制web构建文件...');
    
    let copiedFiles = 0;
    let totalFiles = 0;
    
    // 计算总文件数
    function countFiles(dir) {
      let count = 0;
      if (!fs.existsSync(dir)) return count;
      
      const files = fs.readdirSync(dir);
      files.forEach(file => {
        const filePath = path.join(dir, file);
        if (fs.statSync(filePath).isDirectory()) {
          count += countFiles(filePath);
        } else {
          count++;
        }
      });
      return count;
    }
    
    function copyDir(src, dest) {
      if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest, { recursive: true });
      }
      
      const files = fs.readdirSync(src);
      files.forEach(file => {
        const srcPath = path.join(src, file);
        const destPath = path.join(dest, file);
        
        if (fs.statSync(srcPath).isDirectory()) {
          copyDir(srcPath, destPath);
        } else {
          fs.copyFileSync(srcPath, destPath);
          copiedFiles++;
          
          // 显示复制进度
          const progress = Math.round((copiedFiles / totalFiles) * 100);
          const progressBar = '█'.repeat(Math.floor(progress / 5)) + '░'.repeat(20 - Math.floor(progress / 5));
          process.stdout.write(`\r📋 复制进度: [${progressBar}] ${progress}% (${copiedFiles}/${totalFiles})`);
        }
      });
    }
    
    try {
      const srcDir = path.join(__dirname, '../web/dist');
      const destDir = path.join(__dirname, '../tsa/web/dist');
      
      // 先计算总文件数
      totalFiles = countFiles(srcDir);
      console.log(`📊 发现 ${totalFiles} 个文件需要复制`);
      
      copyDir(srcDir, destDir);
      process.stdout.write('\n');
      console.log('✅ 文件复制完成');
      resolve();
    } catch (error) {
      process.stdout.write('\n');
      console.error('❌ 文件复制失败:', error.message);
      reject(error);
    }
  });
}

// 处理文件变化
async function handleFileChange(filePath) {
  if (isBuilding) {
    console.log('⏳ 正在构建中，跳过此次变化...');
    return;
  }

  isBuilding = true;
  console.log(`📝 检测到文件变化: ${filePath}`);
  console.log('🚀 开始重新构建流程...');

  const startTime = Date.now();
  
  try {
    console.log('📋 步骤 1/2: 构建web项目');
    await buildWeb();
    
    console.log('📋 步骤 2/2: 复制构建文件');
    await copyWebDist();
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(1);
    console.log(`🎉 构建完成！耗时 ${duration}s，可以刷新Chrome扩展查看效果`);
  } catch (error) {
    console.error('❌ 构建过程出错:', error.message);
  } finally {
    isBuilding = false;
  }
}

// 启动主项目的watch模式
function startMainWatch() {
  console.log('🔄 启动主项目监听...');
  
  mainBuildProcess = spawn('npm', ['run', 'watch'], {
    stdio: 'inherit',
    shell: true
  });

  mainBuildProcess.on('close', (code) => {
    console.log(`主项目监听进程退出，退出码: ${code}`);
  });
}

// 监听web项目文件变化
const watcher = chokidar.watch('web/src/**/*', {
  ignored: /node_modules/,
  persistent: true,
  ignoreInitial: true
});

watcher.on('change', handleFileChange);
watcher.on('add', handleFileChange);
watcher.on('unlink', handleFileChange);

console.log('👀 开始监听web/src目录的文件变化...');

// 启动主项目监听
startMainWatch();

// 初始构建
console.log('🔨 执行初始构建...');
buildWeb()
  .then(() => copyWebDist())
  .then(() => {
    console.log('✨ 初始构建完成！');
    console.log('💡 提示：');
    console.log('   - 修改web/src下的文件会自动触发构建');
    console.log('   - 构建完成后刷新Chrome扩展即可看到效果');
    console.log('   - 按Ctrl+C退出监听模式');
  })
  .catch(error => {
    console.error('❌ 初始构建失败:', error.message);
    process.exit(1);
  });

// 优雅退出
process.on('SIGINT', () => {
  console.log('\n🛑 正在退出监听模式...');
  
  if (webBuildProcess) {
    webBuildProcess.kill();
  }
  
  if (mainBuildProcess) {
    mainBuildProcess.kill();
  }
  
  watcher.close();
  process.exit(0);
}); 