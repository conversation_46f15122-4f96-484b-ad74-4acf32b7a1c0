import { getNextPendingProduct, updateProductStatus, PendingProduct } from './rejectedProductApi'
import { deleteSingleN11Product, isDeleteSuccess } from './n11DeleteApi'
import { findTaskDetailByStockCode, getRetryUploadParams, batchRetryUpload } from './taskDetailApi'

// 进度回调类型
export interface ProgressCallback {
  (current: number, total: number, message: string): void
}

// 处理回调类型
export interface ProcessCallbacks {
  onStart?: () => void
  onProgress?: (current: number, total: number, message: string) => void
  onComplete?: (successCount: number, failureCount: number) => void
  onError?: (error: Error) => void
}

// 处理结果类型
export interface ProcessResult {
  success: boolean
  processedCount: number
  successCount: number
  failureCount: number
  message: string
}

/**
 * 处理被拒绝商品重新上传的主要流程
 * @param onProgress 进度回调函数
 * @returns 处理结果
 */
export async function processRejectedProductReupload(
  onProgress?: ProgressCallback
): Promise<ProcessResult> {
  let processedCount = 0
  let successCount = 0
  let failureCount = 0
  let isCancelled = false

  try {
    if (onProgress) {
      onProgress(0, 0, '开始处理被拒绝商品重新上传...')
    }

    // 循环处理所有待处理商品
    while (!isCancelled) {
      // 获取下一个待处理商品
      const pendingProduct = await getNextPendingProduct()
      
      if (!pendingProduct) {
        // 没有更多待处理商品，结束循环
        break
      }

      try {
        if (onProgress) {
          onProgress(processedCount, processedCount + 1, `正在处理商品: ${pendingProduct.title}`)
        }

        // 处理单个被拒绝商品
        const result = await processSingleRejectedProduct(pendingProduct)
        
        if (result.success) {
          successCount++
          if (onProgress) {
            onProgress(processedCount + 1, processedCount + 1, `商品处理成功: ${pendingProduct.title}`)
          }
        } else {
          failureCount++
          if (onProgress) {
            onProgress(processedCount + 1, processedCount + 1, `商品处理失败: ${pendingProduct.title} - ${result.message}`)
          }
        }

        processedCount++

        // 添加小延迟避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 1000))

      } catch (error) {
        console.error('处理单个商品失败:', error)
        failureCount++
        processedCount++
        
        if (onProgress) {
          const errorMessage = error instanceof Error ? error.message : '未知错误'
          onProgress(processedCount, processedCount, `商品处理异常: ${pendingProduct.title} - ${errorMessage}`)
        }
      }
    }

    const message = `处理完成！成功: ${successCount}，失败: ${failureCount}`
    if (onProgress) {
      onProgress(processedCount, processedCount, message)
    }

    return {
      success: true,
      processedCount,
      successCount,
      failureCount,
      message
    }

  } catch (error) {
    console.error('处理被拒绝商品重新上传失败:', error)
    
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    const message = `处理异常: ${errorMessage}`
    if (onProgress) {
      onProgress(processedCount, processedCount, message)
    }

    return {
      success: false,
      processedCount,
      successCount,
      failureCount,
      message
    }
  }
}

/**
 * 处理单个被拒绝商品
 * @param product 待处理商品
 * @param callbacks 处理回调
 * @returns 处理结果
 */
export async function processSingleRejectedProduct(
  product: PendingProduct,
  callbacks?: ProcessCallbacks
): Promise<{ success: boolean; message: string }> {
  try {
    if (callbacks?.onStart) {
      callbacks.onStart()
    }

    // 第一步：删除N11商品
    if (callbacks?.onProgress) {
      callbacks.onProgress(1, 4, '正在删除N11商品...')
    }

    const deleteResponse = await deleteSingleN11Product(product.product_id)
    
    if (!isDeleteSuccess(deleteResponse, product.product_id)) {
      throw new Error('删除N11商品失败')
    }

    // 第二步：根据stockCode查找任务详情
    if (callbacks?.onProgress) {
      callbacks.onProgress(2, 4, '正在查找任务详情...')
    }

    const taskDetail = await findTaskDetailByStockCode(product.stock_code)
    
    if (!taskDetail) {
      throw new Error(`未找到stockCode为 ${product.stock_code} 的任务详情`)
    }

    // 第三步：获取重新上传参数
    if (callbacks?.onProgress) {
      callbacks.onProgress(3, 4, '正在获取重新上传参数...')
    }

    const retryParams = await getRetryUploadParams(taskDetail.id)

    // 第四步：执行重新上传
    if (callbacks?.onProgress) {
      callbacks.onProgress(4, 4, '正在执行重新上传...')
    }

    await batchRetryUpload([taskDetail.id])

    // 第五步：更新商品状态为已完成
    await updateProductStatus(product.product_id, 1)

    if (callbacks?.onComplete) {
      callbacks.onComplete(1, 0)
    }

    return {
      success: true,
      message: '商品重新上传成功'
    }

  } catch (error) {
    console.error('处理单个被拒绝商品失败:', error)
    
    if (callbacks?.onError) {
      callbacks.onError(error instanceof Error ? error : new Error(String(error)))
    }

    // 即使处理失败，也要更新状态为已完成，避免重复处理
    try {
      await updateProductStatus(product.product_id, 1)
    } catch (updateError) {
      console.error('更新商品状态失败:', updateError)
    }

    return {
      success: false,
      message: error instanceof Error ? error.message : '处理失败'
    }
  }
}

/**
 * 取消处理流程
 */
export function cancelProcessing(): void {
  // 这里可以实现取消逻辑
  console.log('取消处理被拒绝商品重新上传')
} 