-- 调整 tsa_user_account 表的价格设置字段
-- 1. 修改 price_rate 字段类型和含义
ALTER TABLE `tsa_user_account` 
MODIFY COLUMN `price_rate` decimal(10,2) UNSIGNED DEFAULT 1.00 COMMENT '价格倍数，原价乘以此数值，如1.1表示原价*1.1';

-- 2. 新增价格加值字段
ALTER TABLE `tsa_user_account` 
ADD COLUMN `price_add` decimal(10,2) UNSIGNED DEFAULT 0.00 COMMENT '价格加值，在倍数计算后再加上此数值' AFTER `price_rate`;

-- 3. 新增价格减值字段
ALTER TABLE `tsa_user_account` 
ADD COLUMN `price_subtract` decimal(10,2) UNSIGNED DEFAULT 0.00 COMMENT '价格减值，在倍数和加值计算后再减去此数值' AFTER `price_add`;

-- 更新现有数据：将原来的百分比转换为倍数
-- 例如：原来的10%（存储为10）转换为1.1倍数
UPDATE `tsa_user_account` SET `price_rate` = 1 + (`price_rate` / 100) WHERE `price_rate` > 0;
UPDATE `tsa_user_account` SET `price_rate` = 1.00 WHERE `price_rate` = 0; 