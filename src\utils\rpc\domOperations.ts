// DOM操作工具文件 - 基于Manifest V3的chrome.scripting API
declare const chrome: any;

/**
 * DOM操作结果类型
 */
export interface DomOperationResult {
  success: boolean;
  message: string;
  data?: any;
}

/**
 * 页面状态信息
 */
export interface PageStatus {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

/**
 * 等待元素出现的配置
 */
export interface WaitElementConfig {
  selector: string;
  timeout?: number;
  checkInterval?: number;
}

/**
 * 在指定标签页中执行脚本
 */
async function executeScriptInTab(tabId: number, func: Function, args: any[] = []): Promise<any> {
  try {
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: func,
      args: args
    });
    
    if (results && results[0] && results[0].result) {
      return results[0].result;
    }
    
    return null;
  } catch (error) {
    console.error('执行脚本失败:', error);
    throw error;
  }
}

/**
 * 切换每页显示数量为100条
 */
export async function setPageSize100(tabId: number): Promise<DomOperationResult> {
  const scriptFunction = () => {
    return new Promise((resolve) => {
      try {
        // 查找页面大小选择器
        const selectContainer = document.querySelector('.simpleSelect') || 
                              document.querySelector('[class*="select"]') ||
                              document.querySelector('.page-size-selector');
        
        if (!selectContainer) {
          resolve({ success: false, message: '未找到页面大小选择器' });
          return;
        }

        // 查找下拉按钮
        const dropdownButton = selectContainer.querySelector('button') ||
                              selectContainer.querySelector('.dropdown-toggle') ||
                              selectContainer.querySelector('[role="button"]');
        
        if (!dropdownButton) {
          resolve({ success: false, message: '未找到下拉按钮' });
          return;
        }

        // 点击下拉按钮
        (dropdownButton as HTMLElement).click();
        
        // 等待下拉菜单出现
        setTimeout(() => {
          // 查找100选项
          const options = document.querySelectorAll('.dropdown-menu li, .select-option, [data-value="100"], [value="100"]');
          let option100: Element | null = null;
          
          for (const option of Array.from(options)) {
            const text = option.textContent?.trim();
            if (text === '100' || text === '100条' || text === '100 items') {
              option100 = option;
              break;
            }
          }
          
          if (!option100) {
            // 尝试其他选择器
            option100 = document.querySelector('[data-value="100"]') ||
                       document.querySelector('option[value="100"]') ||
                       document.querySelector('li:contains("100")');
          }
          
          if (option100) {
            (option100 as HTMLElement).click();
            
            // 等待页面更新
            setTimeout(() => {
              resolve({ success: true, message: '成功设置每页显示100条' });
            }, 1000);
          } else {
            resolve({ success: false, message: '未找到100条选项' });
          }
        }, 500);
        
      } catch (error: any) {
        resolve({ success: false, message: `设置页面大小失败: ${error.message}` });
      }
    });
  };

  try {
    const result = await executeScriptInTab(tabId, scriptFunction);
    return result || { success: false, message: '脚本执行无返回结果' };
  } catch (error: any) {
    return { success: false, message: `执行脚本失败: ${error.message}` };
  }
}

/**
 * 点击下一页按钮
 */
export async function clickNextPage(tabId: number): Promise<DomOperationResult> {
  const scriptFunction = () => {
    return new Promise((resolve) => {
      try {
        // 查找分页容器
        const pagination = document.querySelector('.pagination') ||
                          document.querySelector('[class*="pagination"]') ||
                          document.querySelector('.page-nav');
        
        if (!pagination) {
          resolve({ success: false, message: '未找到分页容器' });
          return;
        }

        // 查找下一页按钮
        const nextButton = pagination.querySelector('.next') ||
                          pagination.querySelector('[aria-label*="next"]') ||
                          pagination.querySelector('[title*="下一页"]') ||
                          pagination.querySelector('button:last-child') ||
                          pagination.querySelector('a:last-child');
        
        if (!nextButton) {
          resolve({ success: false, message: '未找到下一页按钮' });
          return;
        }

        // 检查按钮是否可点击
        const buttonElement = nextButton as HTMLButtonElement;
        if (buttonElement.disabled || nextButton.classList.contains('disabled')) {
          resolve({ success: false, message: '下一页按钮已禁用，可能已到最后一页' });
          return;
        }

        // 滚动到按钮可见区域
        nextButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // 等待滚动完成后点击
        setTimeout(() => {
          (nextButton as HTMLElement).click();
          resolve({ success: true, message: '成功点击下一页按钮' });
        }, 500);
        
      } catch (error: any) {
        resolve({ success: false, message: `点击下一页失败: ${error.message}` });
      }
    });
  };

  try {
    const result = await executeScriptInTab(tabId, scriptFunction);
    return result || { success: false, message: '脚本执行无返回结果' };
  } catch (error: any) {
    return { success: false, message: `执行脚本失败: ${error.message}` };
  }
}

/**
 * 点击指定页码
 */
export async function clickPageNumber(tabId: number, pageNumber: number): Promise<DomOperationResult> {
  const scriptFunction = (targetPage: number) => {
    return new Promise((resolve) => {
      try {
        const pagination = document.querySelector('.pagination') ||
                          document.querySelector('[class*="pagination"]');
        
        if (!pagination) {
          resolve({ success: false, message: '未找到分页容器' });
          return;
        }

        // 查找目标页码按钮
        const pageButtons = pagination.querySelectorAll('button, a');
        let targetButton: Element | null = null;
        
        for (const button of Array.from(pageButtons)) {
          const text = button.textContent?.trim();
          if (text === targetPage.toString()) {
            targetButton = button;
            break;
          }
        }
        
        if (!targetButton) {
          resolve({ success: false, message: `未找到页码 ${targetPage} 的按钮` });
          return;
        }

        const buttonElement = targetButton as HTMLButtonElement;
        if (buttonElement.disabled || targetButton.classList.contains('disabled') || 
            targetButton.classList.contains('active')) {
          resolve({ success: false, message: `页码 ${targetPage} 按钮不可点击` });
          return;
        }

        targetButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        setTimeout(() => {
          (targetButton as HTMLElement).click();
          resolve({ success: true, message: `成功点击页码 ${targetPage}` });
        }, 500);
        
      } catch (error: any) {
        resolve({ success: false, message: `点击页码失败: ${error.message}` });
      }
    });
  };

  try {
    const result = await executeScriptInTab(tabId, scriptFunction, [pageNumber]);
    return result || { success: false, message: '脚本执行无返回结果' };
  } catch (error: any) {
    return { success: false, message: `执行脚本失败: ${error.message}` };
  }
}

/**
 * 获取页面状态信息
 */
export async function getPageStatus(tabId: number): Promise<PageStatus | null> {
  const scriptFunction = () => {
    try {
      // 查找分页信息
      const pagination = document.querySelector('.pagination') ||
                        document.querySelector('[class*="pagination"]');
      
      if (!pagination) {
        return null;
      }

      // 获取当前页码
      const activeButton = pagination.querySelector('.active') ||
                          pagination.querySelector('[aria-current="page"]') ||
                          pagination.querySelector('.current');
      
      const currentPage = activeButton ? parseInt(activeButton.textContent?.trim() || '1') : 1;

      // 获取总页数 - 查找最大页码
      const pageButtons = pagination.querySelectorAll('button, a');
      let totalPages = 1;
      
      for (const button of Array.from(pageButtons)) {
        const text = button.textContent?.trim();
        if (text && /^\d+$/.test(text)) {
          const pageNum = parseInt(text);
          if (pageNum > totalPages) {
            totalPages = pageNum;
          }
        }
      }

      // 查找页面大小信息
      const pageSizeElement = document.querySelector('.page-size') ||
                             document.querySelector('[class*="page-size"]') ||
                             document.querySelector('.simpleSelect button');
      
      const pageSize = pageSizeElement ? 
        parseInt(pageSizeElement.textContent?.match(/\d+/)?.[0] || '20') : 20;

      // 查找总条目数
      const totalElement = document.querySelector('.total-count') ||
                          document.querySelector('[class*="total"]') ||
                          document.querySelector('.result-count');
      
      const totalItems = totalElement ? 
        parseInt(totalElement.textContent?.match(/\d+/)?.[0] || '0') : 0;

      // 检查是否有下一页/上一页
      const nextButton = pagination.querySelector('.next') ||
                        pagination.querySelector('[aria-label*="next"]');
      const prevButton = pagination.querySelector('.prev') ||
                        pagination.querySelector('[aria-label*="prev"]');

      const hasNextPage = nextButton && !(nextButton as HTMLButtonElement).disabled && 
                         !nextButton.classList.contains('disabled');
      const hasPrevPage = prevButton && !(prevButton as HTMLButtonElement).disabled && 
                         !prevButton.classList.contains('disabled');

      return {
        currentPage,
        totalPages,
        pageSize,
        totalItems,
        hasNextPage: !!hasNextPage,
        hasPrevPage: !!hasPrevPage
      };
      
    } catch (error) {
      console.error('获取页面状态失败:', error);
      return null;
    }
  };

  try {
    const result = await executeScriptInTab(tabId, scriptFunction);
    return result;
  } catch (error) {
    console.error('执行获取页面状态脚本失败:', error);
    return null;
  }
}

/**
 * 等待元素出现
 */
export async function waitForElement(tabId: number, config: WaitElementConfig): Promise<DomOperationResult> {
  const { selector, timeout = 10000, checkInterval = 500 } = config;
  
  const scriptFunction = (sel: string, maxTime: number, interval: number) => {
    return new Promise((resolve) => {
      const startTime = Date.now();
      
      const checkElement = () => {
        const element = document.querySelector(sel);
        
        if (element) {
          resolve({ success: true, message: `元素 ${sel} 已出现` });
          return;
        }
        
        if (Date.now() - startTime > maxTime) {
          resolve({ success: false, message: `等待元素 ${sel} 超时` });
          return;
        }
        
        setTimeout(checkElement, interval);
      };
      
      checkElement();
    });
  };

  try {
    const result = await executeScriptInTab(tabId, scriptFunction, [selector, timeout, checkInterval]);
    return result || { success: false, message: '脚本执行无返回结果' };
  } catch (error: any) {
    return { success: false, message: `执行等待元素脚本失败: ${error.message}` };
  }
}

/**
 * 滚动页面到指定位置
 */
export async function scrollToPosition(tabId: number, position: 'top' | 'bottom' | number): Promise<DomOperationResult> {
  const scriptFunction = (pos: 'top' | 'bottom' | number) => {
    try {
      let scrollTop: number;
      
      if (pos === 'top') {
        scrollTop = 0;
      } else if (pos === 'bottom') {
        scrollTop = document.body.scrollHeight;
      } else {
        scrollTop = pos;
      }
      
      window.scrollTo({ top: scrollTop, behavior: 'smooth' });
      
      return { success: true, message: `成功滚动到位置: ${pos}` };
    } catch (error: any) {
      return { success: false, message: `滚动失败: ${error.message}` };
    }
  };

  try {
    const result = await executeScriptInTab(tabId, scriptFunction, [position]);
    return result || { success: false, message: '脚本执行无返回结果' };
  } catch (error: any) {
    return { success: false, message: `执行滚动脚本失败: ${error.message}` };
  }
}

/**
 * 检查页面是否为N11产品列表页面
 */
export async function isN11ProductListPage(tabId: number): Promise<boolean> {
  const scriptFunction = () => {
    try {
      const url = window.location.href;
      const hasCorrectUrl = url.includes('so.n11.com') && 
                           (url.includes('product') || url.includes('urun'));
      
      const hasProductList = document.querySelector('.product-list') ||
                            document.querySelector('[class*="product"]') ||
                            document.querySelector('.tabManager');
      
      return hasCorrectUrl && !!hasProductList;
    } catch (error) {
      return false;
    }
  };

  try {
    const result = await executeScriptInTab(tabId, scriptFunction);
    return !!result;
  } catch (error) {
    console.error('检查页面类型失败:', error);
    return false;
  }
} 