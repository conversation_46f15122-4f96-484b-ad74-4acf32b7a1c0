{"name": "tsa-obfuscator", "version": "1.0.0", "description": "TSA Chrome扩展代码混淆工具", "main": "gulpfile.js", "scripts": {"obfuscate": "gulp obfuscate", "obfuscate:fast": "gulp obfuscate-fast", "obfuscate:quick": "gulp obfuscate-quick", "obfuscate:strong": "gulp obfuscate-strong", "obfuscate:background": "gulp obfuscate-background", "restore": "gulp restore", "clean": "gulp clean-backup", "install:deps": "npm install"}, "devDependencies": {"gulp": "^4.0.2", "gulp-javascript-obfuscator": "^1.1.6", "gulp-html-minifier-terser": "^7.1.0", "gulp-clean-css": "^4.3.0", "gulp-sourcemaps": "^3.0.0", "through2": "^4.0.2", "glob": "^8.1.0"}, "keywords": ["gulp", "obfuscation", "chrome-extension", "javascript", "minification"], "author": "TSA Team", "license": "MIT"}