{"name": "chrome-extension", "license": "MIT", "private": true, "scripts": {"watch": "cross-env NODE_ENV=development BTYPE=tsa_watch webpack --progress --mode development --watch", "build": "cd web && npm run build && cd ../ &&  cross-env NODE_ENV=production BTYPE=tsa webpack --progress --mode production && cross-env NODE_ENV=production BTYPE=tsa node plugin/cleanFiles.js && npm run obfuscate:background", "test_clean": "cross-env NODE_ENV=production BTYPE=tsa node plugin/cleanFiles.js", "build:web": "cd web && npm run build", "dev:web": "cd web && npm run dev", "dev:watch": "concurrently \"npm run dev:web\" \"npm run watch:main\"", "watch:main": "cross-env NODE_ENV=development BTYPE=tsa_watch webpack --progress --mode development --watch", "dev:full": "concurrently \"cd web && npm run dev\" \"cross-env NODE_ENV=development BTYPE=tsa_watch webpack --progress --mode development --watch\"", "dev:auto": "node scripts/dev-watch.js", "b_tsa": "cd web && npm run build && cd ../ &&  cross-env NODE_ENV=production BTYPE=tsa webpack --progress --mode production && cross-env NODE_ENV=production BTYPE=tsa node plugin/cleanFiles.js && npm run obfuscate:background", "obfuscate": "cd plugin/gulp && npx gulp obfuscate", "obfuscate:fast": "cd plugin/gulp && npx gulp obfuscate-fast", "obfuscate:quick": "cd plugin/gulp && npx gulp obfuscate-quick", "obfuscate:strong": "cd plugin/gulp && npx gulp obfuscate-strong", "obfuscate:background": "cd plugin/gulp && npx gulp obfuscate-background", "obfuscate:restore": "cd plugin/gulp && npx gulp restore", "obfuscate:clean": "cd plugin/gulp && npx gulp clean-backup", "build:obfuscate": "npm run build && npm run obfuscate", "build:obfuscate:fast": "npm run build && npm run obfuscate:fast", "obfuscate:install": "cd plugin/gulp && npm install"}, "dependencies": {"core-js": "^3.21.1", "crypto-js": "^4.2.0", "element-plus": "^2.1.0", "git-cz": "^4.9.0", "long": "^5.2.3", "pako": "^2.1.0", "protobufjs": "^7.3.2", "unplugin-auto-import": "^0.15.2", "unplugin-vue-define-options": "^1.3.3", "vue": "^3.2.31", "vue-router": "^4.5.1"}, "devDependencies": {"@babel/core": "^7.17.5", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@types/copy-webpack-plugin": "^10.1.0", "@types/crypto-js": "^4.2.2", "@types/jquery": "^3.5.14", "@types/node": "^17.0.25", "@types/pako": "^2.0.3", "@types/webpack": "^5.28.0", "@vespaiach/axios-fetch-adapter": "^0.2.2", "axios": "^0.26.1", "babel-loader": "^8.2.3", "chokidar": "^3.5.3", "concurrently": "^7.6.0", "copy-webpack-plugin": "^5.0.3", "cross-env": "^7.0.3", "css-loader": "^6.7.1", "dotenv": "^16.4.5", "dotenv-webpack": "^8.1.0", "esbuild-loader": "^4.2.2", "html-webpack-plugin": "^5.5.0", "husky": "^7.0.4", "jquery": "^3.6.0", "less": "^3.11.1", "less-loader": "^10.2.0", "lint-staged": "^12.3.7", "pinia": "^2.0.13", "prettier": "^2.8.8", "style-loader": "^3.3.1", "sucrase": "^3.20.3", "typescript": "^4.6.2", "unplugin-vue-components": "^0.18.0", "url-loader": "^4.1.1", "vue-loader": "^17.0.0", "webpack": "^5.70.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.7.4"}, "lint-staged": {"*.{vue,js,ts,jsx,tsx,md,json}": "eslint --fix"}}