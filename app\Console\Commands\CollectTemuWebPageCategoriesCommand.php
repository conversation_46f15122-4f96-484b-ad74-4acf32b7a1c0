<?php

namespace App\Console\Commands;

use App\Service\TeMu\ProductCategoryService;
use Illuminate\Console\Command;

class CollectTemuWebPageCategoriesCommand extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'temu:collect-webpage-categories';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '从网页JSON文件采集Temu商品分类并保存到数据库';

    /**
     * 商品分类服务
     *
     * @var ProductCategoryService
     */
    protected $productCategoryService;

    /**
     * 创建命令实例
     *
     * @param ProductCategoryService $productCategoryService
     * @return void
     */
    public function __construct(ProductCategoryService $productCategoryService)
    {
        parent::__construct();
        $this->productCategoryService = $productCategoryService;
    }

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始从网页JSON文件采集Temu商品分类...');

        // 传递命令行参数给服务方法
        $result = $this->productCategoryService->collectCategoriesFromWebPage(true, $this);

        if ($result['status'] == 1) {
            $this->info('采集成功: ' . $result['message']);
            $this->info('共采集了 ' . $result['count'] . ' 个分类');
            return 0; // 成功返回码
        } else {
            $this->error('采集失败: ' . $result['message']);
            return 1; // 失败返回码
        }
    }
}
