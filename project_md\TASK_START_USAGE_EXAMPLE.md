# 任务开启API使用示例

## 前端调用示例

### JavaScript/Ajax 调用

```javascript
// 开启任务
function startTask(taskId) {
    $.ajax({
        url: '/api/user/task/start',
        type: 'POST',
        headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('token'),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({
            task_id: taskId
        }),
        success: function(response) {
            if (response.code === 200) {
                console.log('任务开启成功:', response.data);
                // 处理返回的第一条任务详情
                handleTaskDetail(response.data);
            } else {
                alert('开启失败: ' + response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('请求失败:', error);
            alert('网络错误，请稍后重试');
        }
    });
}

// 处理任务详情数据
function handleTaskDetail(taskDetail) {
    // 显示商品信息
    if (taskDetail.goods_info) {
        $('#goods-name').text(taskDetail.goods_info.goods_name);
        $('#goods-pic').attr('src', JSON.parse(taskDetail.goods_info.goods_pic)[0]);
    }
    
    // 显示SKU信息
    if (taskDetail.sku_info) {
        $('#sku-price').text(taskDetail.currentcy + ' ' + taskDetail.price);
        $('#sku-specs').text(taskDetail.spec_values);
    }
    
    // 显示店铺信息
    if (taskDetail.store_info) {
        $('#store-name').text(taskDetail.store_info.account_name);
        $('#store-logo').attr('src', taskDetail.store_info.account_logo);
    }
    
    // 保存当前任务详情ID，用于后续操作
    window.currentTaskDetailId = taskDetail.id;
}
```

### Vue.js 调用示例

```vue
<template>
  <div class="task-list">
    <div v-for="task in tasks" :key="task.id" class="task-item">
      <h3>{{ task.store_name }}</h3>
      <p>任务数量: {{ task.task_count }}</p>
      <p>完成数量: {{ task.task_num }}</p>
      <button 
        @click="startTask(task.id)" 
        :disabled="task.task_over === 1"
        class="start-btn"
      >
        {{ task.task_over === 1 ? '已完成' : '开启' }}
      </button>
    </div>
    
    <!-- 任务详情弹窗 -->
    <div v-if="currentTaskDetail" class="task-detail-modal">
      <div class="modal-content">
        <h3>任务详情</h3>
        <div class="goods-info">
          <img :src="getFirstImage(currentTaskDetail.goods_info.goods_pic)" alt="商品图片">
          <h4>{{ currentTaskDetail.goods_info.goods_name }}</h4>
        </div>
        <div class="sku-info">
          <p>价格: {{ currentTaskDetail.currentcy }} {{ currentTaskDetail.price }}</p>
          <p>规格: {{ currentTaskDetail.spec_values }}</p>
        </div>
        <div class="store-info">
          <p>店铺: {{ currentTaskDetail.store_info.account_name }}</p>
        </div>
        <button @click="closeModal">关闭</button>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  data() {
    return {
      tasks: [],
      currentTaskDetail: null
    }
  },
  methods: {
    async startTask(taskId) {
      try {
        const response = await axios.post('/api/user/task/start', {
          task_id: taskId
        }, {
          headers: {
            'Authorization': `Bearer ${this.$store.state.token}`
          }
        })
        
        if (response.data.code === 200) {
          this.currentTaskDetail = response.data.data
          this.$message.success('任务开启成功')
        } else {
          this.$message.error(response.data.message)
        }
      } catch (error) {
        console.error('开启任务失败:', error)
        this.$message.error('网络错误，请稍后重试')
      }
    },
    
    getFirstImage(goodsPic) {
      try {
        const pics = JSON.parse(goodsPic)
        return pics.length > 0 ? pics[0] : '/default-image.jpg'
      } catch (e) {
        return '/default-image.jpg'
      }
    },
    
    closeModal() {
      this.currentTaskDetail = null
    }
  }
}
</script>
```

### React 调用示例

```jsx
import React, { useState } from 'react';
import axios from 'axios';

const TaskList = () => {
  const [tasks, setTasks] = useState([]);
  const [currentTaskDetail, setCurrentTaskDetail] = useState(null);
  const [loading, setLoading] = useState(false);

  const startTask = async (taskId) => {
    setLoading(true);
    try {
      const response = await axios.post('/api/user/task/start', {
        task_id: taskId
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.code === 200) {
        setCurrentTaskDetail(response.data.data);
        alert('任务开启成功');
      } else {
        alert(response.data.message);
      }
    } catch (error) {
      console.error('开启任务失败:', error);
      alert('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const getFirstImage = (goodsPic) => {
    try {
      const pics = JSON.parse(goodsPic);
      return pics.length > 0 ? pics[0] : '/default-image.jpg';
    } catch (e) {
      return '/default-image.jpg';
    }
  };

  return (
    <div className="task-list">
      {tasks.map(task => (
        <div key={task.id} className="task-item">
          <h3>{task.store_name}</h3>
          <p>任务数量: {task.task_count}</p>
          <p>完成数量: {task.task_num}</p>
          <button 
            onClick={() => startTask(task.id)}
            disabled={task.task_over === 1 || loading}
            className="start-btn"
          >
            {task.task_over === 1 ? '已完成' : '开启'}
          </button>
        </div>
      ))}

      {currentTaskDetail && (
        <div className="task-detail-modal">
          <div className="modal-content">
            <h3>任务详情</h3>
            <div className="goods-info">
              <img 
                src={getFirstImage(currentTaskDetail.goods_info.goods_pic)} 
                alt="商品图片" 
              />
              <h4>{currentTaskDetail.goods_info.goods_name}</h4>
            </div>
            <div className="sku-info">
              <p>价格: {currentTaskDetail.currentcy} {currentTaskDetail.price}</p>
              <p>规格: {currentTaskDetail.spec_values}</p>
            </div>
            <div className="store-info">
              <p>店铺: {currentTaskDetail.store_info.account_name}</p>
            </div>
            <button onClick={() => setCurrentTaskDetail(null)}>关闭</button>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskList;
```

## 后端测试示例

### PHP/Laravel 测试

```php
<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User\UserModel;
use App\Models\User\UserTaskModel;
use App\Models\User\GoodsModel;
use App\Models\User\GoodsSkuModel;
use App\Models\User\UserAccountModel;

class UserTaskStartTest extends TestCase
{
    public function test_start_task_success()
    {
        // 创建测试用户
        $user = UserModel::factory()->create();
        
        // 创建测试店铺
        $store = UserAccountModel::factory()->create([
            'user_id' => $user->id,
            'status' => 1
        ]);
        
        // 创建测试商品
        $goods = GoodsModel::factory()->create([
            'user_id' => $user->id,
            'status' => 1
        ]);
        
        // 创建测试SKU
        $sku = GoodsSkuModel::factory()->create([
            'user_goods_id' => $goods->id,
            'goods_id' => $goods->goods_id
        ]);
        
        // 创建测试任务
        $task = UserTaskModel::factory()->create([
            'user_id' => $user->id,
            'user_account_id' => $store->id,
            'task_over' => 0,
            'is_selected' => 0,
            'day_start' => now()->format('Y-m-d'),
            'day_end' => now()->format('Y-m-d'),
            'sort_order' => 'desc'
        ]);
        
        // 模拟登录
        $this->actingAs($user);
        
        // 调用开启任务API
        $response = $this->postJson('/api/user/task/start', [
            'task_id' => $task->id
        ]);
        
        // 断言响应
        $response->assertStatus(200)
                ->assertJson([
                    'code' => 200,
                    'message' => '任务开启成功'
                ])
                ->assertJsonStructure([
                    'data' => [
                        'id',
                        'user_id',
                        'task_id',
                        'user_goods_id',
                        'user_goods_sku_id',
                        'goods_info',
                        'sku_info',
                        'store_info'
                    ]
                ]);
    }
    
    public function test_start_task_invalid_id()
    {
        $user = UserModel::factory()->create();
        $this->actingAs($user);
        
        $response = $this->postJson('/api/user/task/start', [
            'task_id' => 'invalid'
        ]);
        
        $response->assertStatus(400)
                ->assertJson([
                    'code' => 400,
                    'message' => '任务ID参数无效'
                ]);
    }
}
```

## 注意事项

1. **认证**: 所有请求都需要携带有效的认证token
2. **错误处理**: 前端需要妥善处理各种错误情况
3. **加载状态**: 建议在请求过程中显示加载状态
4. **数据缓存**: 可以考虑缓存任务详情数据以提高用户体验
5. **重复调用**: API具有幂等性，重复调用不会产生副作用 