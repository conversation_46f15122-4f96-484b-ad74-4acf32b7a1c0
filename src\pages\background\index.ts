import * as utils from '@/utils/index';
import { axios } from '@/api/useAxios';
import { useFetch as useFetchHandler } from '@/api/useFetch';

import { forceRemoveAllCookies } from '@/utils/protocol/common/common';
import config from '@/config';

import './clear_cookie';

//调用chrome事件
function chromeEvent(request: { funName: any; pramas: any; }, sender: any, callback: (arg0: any[]) => any) {
  let { funName, pramas } = request;
  let funCode = funName.split('.');
  let chromeFun = chrome;
  const chromeCallback = (...arr: any[]) => callback(arr)
  funCode.forEach((item: string, index: number) => {
    if (typeof chromeFun[item] !== 'undefined')
      if (index + 1 === funCode.length) {
        if (typeof chromeFun[item] === 'function') {
          let callbackindex = pramas.findIndex((item: any) => typeof item === 'object' && 'callback' in item && item['callback'])
          if (callbackindex != -1) pramas[callbackindex] = chromeCallback;
          chromeFun[item](...pramas)
        } else throw new Error('未找到对应的chrome方法')
      } else {
        chromeFun = chromeFun[item]
      }
  })
}

export const removeSpecificRule = async (ruleId: number): Promise<void> => {
  try {
    await chrome.declarativeNetRequest.updateDynamicRules({
      removeRuleIds: [ruleId],
      addRules: []
    });

    console.log(`Rule ${ruleId} removed successfully`);
  } catch (error) {
    console.error(`Failed to remove rule ${ruleId}:`, error);
  }
};

// RPC操作相关函数
/**
 * 执行DOM操作
 */
async function handleRpcDomOperation(request: any, sendResponse: any): Promise<void> {
  try {
    const { operation, tabId, params } = request;
    
    // 获取当前活动标签页（如果没有指定tabId）
    let targetTabId = tabId;
    if (!targetTabId) {
      const tabs = await new Promise<any[]>((resolve) => {
        chrome.tabs.query({ active: true, currentWindow: true }, resolve);
      });
      
      if (!tabs || tabs.length === 0) {
        sendResponse({ success: false, message: '未找到活动标签页' });
        return;
      }
      
      targetTabId = tabs[0].id;
    }

    // 动态导入DOM操作模块
    const domModule = await import('@/utils/rpc/domOperations');
    
          let result;
      switch (operation) {
        case 'setPageSize100':
          result = await domModule.setPageSize100(targetTabId);
          break;
        case 'clickNextPage':
          result = await domModule.clickNextPage(targetTabId);
          break;
        case 'getPageStatus':
          result = await domModule.getPageStatus(targetTabId);
          break;
        case 'isN11ProductListPage':
          result = await domModule.isN11ProductListPage(targetTabId);
          break;
        case 'waitForElement':
          result = await domModule.waitForElement(targetTabId, params);
          break;
        case 'scrollToPosition':
          result = await domModule.scrollToPosition(targetTabId, params);
          break;
        default:
          result = { success: false, message: `不支持的DOM操作: ${operation}` };
      }
    
    sendResponse(result);
  } catch (error: any) {
    console.error('RPC DOM操作失败:', error);
    sendResponse({ success: false, message: `DOM操作失败: ${error.message}` });
  }
}

/**
 * 处理新的简化DOM操作（专门用于调试页面大小设置）
 * 完全避免导入DOM相关模块，直接在这里实现
 */
async function handleRpcDomOperationNew(request: any, sendResponse: any): Promise<void> {
  try {
    console.log('Background: 开始处理新的DOM操作请求');
    
    // 获取当前活动标签页
    const tabs = await new Promise<any[]>((resolve) => {
      chrome.tabs.query({ active: true, currentWindow: true }, resolve);
    });
    
    if (!tabs || tabs.length === 0) {
      sendResponse({ success: false, message: '未找到活动标签页' });
      return;
    }
    
    const targetTabId = tabs[0].id;
    console.log('Background: 使用标签页ID:', targetTabId);

    // 直接使用executeScript，不导入任何模块
    const results = await chrome.scripting.executeScript({
      target: { tabId: targetTabId },
      func: () => {
        return new Promise((resolve) => {
          try {
            console.log('调试脚本: 开始执行页面大小设置');
            
            // 根据实际DOM结构查找100选项
            // 先找到下拉内容容器
            const dropdownContent = document.querySelector('.dropdown-content .simpleSelect-content') ||
                                   document.querySelector('.simpleSelect-content') ||
                                   document.querySelector('.dropdown-content');
            
            if (!dropdownContent) {
              console.log('调试脚本: 未找到下拉内容容器，尝试点击下拉按钮');
              
              // 如果没有找到展开的下拉菜单，先尝试点击下拉按钮
              const selectContainer = document.querySelector('.simpleSelect') || 
                                    document.querySelector('[class*="select"]') ||
                                    document.querySelector('.per-page-wrapper');
              
              if (!selectContainer) {
                resolve({ success: false, message: '未找到页面大小选择器容器' });
                return;
              }

              const dropdownButton = selectContainer.querySelector('button') ||
                                    selectContainer.querySelector('.dropdown-toggle') ||
                                    selectContainer.querySelector('[role="button"]') ||
                                    selectContainer.querySelector('.simpleSelect-btn');
              
              if (!dropdownButton) {
                resolve({ success: false, message: '未找到下拉按钮' });
                return;
              }

              console.log('调试脚本: 点击下拉按钮');
              const buttonElement = dropdownButton as HTMLElement;
              buttonElement.click();
              
              // 等待下拉菜单出现后再查找选项
              setTimeout(() => {
                const newDropdownContent = document.querySelector('.dropdown-content .simpleSelect-content') ||
                                         document.querySelector('.simpleSelect-content') ||
                                         document.querySelector('.dropdown-content');
                
                if (!newDropdownContent) {
                  resolve({ success: false, message: '点击后仍未找到下拉内容容器' });
                  return;
                }
                
                console.log('调试脚本: 下拉菜单已展开，查找100选项');
                findAndClick100Option(newDropdownContent, resolve);
              }, 500);
            } else {
              console.log('调试脚本: 下拉菜单已展开，直接查找100选项');
              findAndClick100Option(dropdownContent, resolve);
            }
            
            // 查找并点击100选项的函数
            function findAndClick100Option(container: Element, resolveCallback: Function) {
              const optionButtons = container.querySelectorAll('li button[type="button"]') ||
                                   container.querySelectorAll('button');
              
              console.log('调试脚本: 找到选项按钮数量:', optionButtons.length);
              
              let option100Button: Element | null = null;
              
              for (const button of Array.from(optionButtons)) {
                const text = button.textContent?.trim();
                console.log('调试脚本: 检查按钮文本:', text);
                if (text === '100 Ürün' || text === '100' || (text && text.includes('100'))) {
                  option100Button = button;
                  console.log('调试脚本: 找到100选项按钮:', button);
                  break;
                }
              }
              
              if (!option100Button) {
                // 备用查找
                const foundButton = Array.from(document.querySelectorAll('button')).find(btn => 
                  btn.textContent?.includes('100')
                );
                option100Button = foundButton || null;
                console.log('调试脚本: 备用查找结果:', option100Button);
              }
              
              if (option100Button) {
                console.log('调试脚本: 点击100选项按钮');
                const buttonElement = option100Button as HTMLElement;
                buttonElement.click();
                
                setTimeout(() => {
                  resolveCallback({ 
                    success: true, 
                    message: '成功通过Background executeScriptInTab设置页面大小为100条' 
                  });
                }, 1000);
              } else {
                resolveCallback({ 
                  success: false, 
                  message: '未找到100选项按钮' 
                });
              }
            }
            
          } catch (error: any) {
            console.error('调试脚本执行错误:', error);
            resolve({ 
              success: false, 
              message: `脚本执行错误: ${error.message}` 
            });
          }
        });
      }
    });
    
    if (results && results[0] && results[0].result) {
      const result = results[0].result;
      console.log('Background: executeScript执行结果:', result);
      sendResponse(result);
    } else {
      sendResponse({ success: false, message: '脚本执行无返回结果' });
    }
    
  } catch (error: any) {
    console.error('Background: executeScript执行失败:', error);
    sendResponse({ success: false, message: `executeScript执行失败: ${error.message}` });
  }
}

/**
 * 管理网络监听器
 */
async function handleRpcNetworkListen(request: any, sendResponse: any): Promise<void> {
  try {
    const { action, tabId, config } = request;
    
    // 获取当前活动标签页（如果没有指定tabId）
    let targetTabId = tabId;
    if (!targetTabId) {
      const tabs = await new Promise<any[]>((resolve) => {
        chrome.tabs.query({ active: true, currentWindow: true }, resolve);
      });
      
      if (!tabs || tabs.length === 0) {
        sendResponse({ success: false, message: '未找到活动标签页' });
        return;
      }
      
      targetTabId = tabs[0].id;
    }

    const networkModule = await import('@/utils/rpc/networkListener');
    
    let result;
    switch (action) {
      case 'create':
        const listener = networkModule.createNetworkListener({ tabId: targetTabId, ...config });
        result = await listener.start();
        break;
      default:
        result = { success: false, message: `不支持的网络监听操作: ${action}` };
    }
    
    sendResponse(result);
  } catch (error: any) {
    console.error('RPC网络监听失败:', error);
    sendResponse({ success: false, message: `网络监听失败: ${error.message}` });
  }
}


/**
 * 页面控制器操作
 */
async function handleRpcPageControl(request: any, sendResponse: any): Promise<void> {
  try {
    const { action, config, onProgress } = request;
    
    console.log('Background: 开始处理RPC页面控制请求:', action);
    
    // 先检查基础Chrome API可用性
    if (typeof chrome === 'undefined' || !chrome.runtime) {
      sendResponse({ success: false, message: 'Chrome运行时环境不可用' });
      return;
    }

    let result;
    switch (action) {
      case 'getAllRejectedProducts':
        try {
          console.log('Background: 准备导入RPC模块...');
          
          // 检查基础环境
          if (!chrome || !chrome.tabs || !chrome.scripting) {
            result = { success: false, message: 'Chrome API 环境不完整' };
            break;
          }
          
          // 使用分步动态导入避免模块级别的DOM API引用
          console.log('Background: 准备导入PageController模块...');
          const { getAllRejectedProductsViaRPC } = await import('@/utils/rpc/pageController');
          console.log('Background: PageController模块导入成功');
          
          console.log('Background: 开始执行getAllRejectedProductsViaRPC...');
          result = await getAllRejectedProductsViaRPC(onProgress);
          console.log('Background: getAllRejectedProductsViaRPC执行完成');
        } catch (importError: any) {
          console.error('Background: 导入或执行RPC模块失败:', importError);
          console.error('Background: 错误详情:', importError.stack);
          result = { success: false, message: `RPC模块错误: ${importError.message}` };
        }
        break;
        
      case 'checkN11Page':
        try {
          console.log('Background: 准备检查N11页面...');
          
          // 直接实现页面检查逻辑，避免导入可能有问题的模块
          if (!chrome || !chrome.tabs || !chrome.scripting) {
            console.warn('Chrome API 环境不完整');
            result = false;
            break;
          }

          // 获取当前活动标签页
          const tabs = await new Promise<any[]>((resolve, reject) => {
            chrome.tabs.query({ active: true, currentWindow: true }, (tabResult: any[]) => {
              if (chrome.runtime.lastError) {
                reject(chrome.runtime.lastError);
              } else {
                resolve(tabResult);
              }
            });
          });

          if (!tabs || tabs.length === 0) {
            console.warn('未找到活动标签页');
            result = false;
            break;
          }

          const tabId = tabs[0].id;
          const url = tabs[0].url;
          
          // 首先通过URL进行基础检查
          if (!url || !url.includes('so.n11.com')) {
            console.log('当前页面不是N11网站');
            result = false;
            break;
          }

          // 通过脚本检查页面结构
          try {
            const results = await chrome.scripting.executeScript({
              target: { tabId },
              func: () => {
                try {
                  const url = window.location.href;
                  const hasCorrectUrl = url.includes('so.n11.com') && 
                                       (url.includes('product') || url.includes('urun'));
                  
                  const hasProductList = document.querySelector('.product-list') ||
                                        document.querySelector('[class*="product"]') ||
                                        document.querySelector('.tabManager');
                  
                  return hasCorrectUrl && !!hasProductList;
                } catch (error) {
                  return false;
                }
              }
            });
            
            result = !!(results && results[0] && results[0].result);
            console.log('Background: N11页面检查完成，结果:', result);
          } catch (scriptError) {
            console.warn('执行页面检查脚本失败:', scriptError);
            // 如果脚本执行失败，回退到URL检查
            result = url.includes('so.n11.com') && (url.includes('product') || url.includes('urun'));
            console.log('Background: 回退URL检查结果:', result);
          }
        } catch (importError: any) {
          console.error('Background: N11页面检查失败:', importError);
          console.error('Background: 错误详情:', importError.stack);
          result = false;
        }
        break;
        
      case 'getRPCStatus':
        try {
          console.log('Background: 准备获取RPC状态...');
          
          // 直接实现状态检查逻辑，避免导入可能有问题的模块
          const status = {
            supported: false,
            chromeAPIs: {
              scripting: !!(chrome && chrome.scripting && chrome.scripting.executeScript),
              debugger: !!(chrome && chrome.debugger && chrome.debugger.attach),
              tabs: !!(chrome && chrome.tabs && chrome.tabs.query)
            },
            currentPage: {
              isN11: false,
              url: undefined as string | undefined,
              tabId: undefined as number | undefined
            },
            permissions: {
              debugger: false,
              scripting: false,
              activeTab: false
            }
          };

          // 检查权限
          if (chrome && chrome.permissions) {
            try {
              const permissions = await new Promise<any>((resolve, reject) => {
                chrome.permissions.getAll((permResult: any) => {
                  if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                  } else {
                    resolve(permResult);
                  }
                });
              });

              status.permissions.debugger = permissions.permissions?.includes('debugger') || false;
              status.permissions.scripting = permissions.permissions?.includes('scripting') || false;
              status.permissions.activeTab = permissions.permissions?.includes('activeTab') || false;
            } catch (permError) {
              console.warn('检查权限失败:', permError);
            }
          }

          // 检查当前页面
          if (chrome && chrome.tabs) {
            try {
              const tabs = await new Promise<any[]>((resolve, reject) => {
                chrome.tabs.query({ active: true, currentWindow: true }, (tabResult: any[]) => {
                  if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                  } else {
                    resolve(tabResult);
                  }
                });
              });

              if (tabs && tabs.length > 0) {
                const activeTab = tabs[0];
                status.currentPage.url = activeTab.url;
                status.currentPage.tabId = activeTab.id;
                
                if (activeTab.url) {
                  status.currentPage.isN11 = activeTab.url.includes('so.n11.com') && 
                                           (activeTab.url.includes('product') || activeTab.url.includes('urun'));
                }
              }
            } catch (tabError) {
              console.warn('获取当前页面信息失败:', tabError);
            }
          }

          // 综合判断是否支持
          const apiSupported = status.chromeAPIs.scripting && 
                              status.chromeAPIs.debugger && 
                              status.chromeAPIs.tabs;
          
          const permissionsGranted = status.permissions.debugger &&
                                    status.permissions.scripting;
          
          status.supported = apiSupported && permissionsGranted;

          result = status;
          console.log('Background: RPC状态检查完成，结果:', result);
        } catch (importError: any) {
          console.error('Background: RPC状态检查失败:', importError);
          console.error('Background: 错误详情:', importError.stack);
          result = { 
            supported: false, 
            message: `状态检查失败: ${importError.message}`,
            error: importError.stack
          };
        }
        break;
        
      default:
        result = { success: false, message: `不支持的页面控制操作: ${action}` };
    }
    
    console.log('Background: RPC页面控制操作完成，发送响应:', result);
    sendResponse(result);
  } catch (error: any) {
    console.error('Background: RPC页面控制失败:', error);
    console.error('Background: 错误堆栈:', error.stack);
    sendResponse({ 
      success: false, 
      message: `页面控制失败: ${error.message}`,
      error: error.stack
    });
  }
}

chrome.runtime.onInstalled.addListener(async function () {});


let crx_identify = '';
let website_id = '';


chrome.runtime.onMessage.addListener(function (request: any, sender: any, sendResponse: any) {
  let responseInfo:any = null;
  let type = 0;
  let url = '';
  console.log(request);
  switch (request.funType) {
    case 'chrome': //代理执行chrome方法
      chromeEvent(request, sender, sendResponse)
      break;
    case 'axios': //代理执行chrome方法
      axios(request, sender, sendResponse)
      break;
    case 'fetch':
      useFetchHandler(request, sender, sendResponse);
      break;

    // 新增RPC操作支持
    case 'rpcDomOperation':
      handleRpcDomOperation(request, sendResponse);
      return true; // 异步响应

    case 'rpcDomOperationNew':
      handleRpcDomOperationNew(request, sendResponse);
      return true; // 异步响应

    case 'rpcNetworkListen':
      handleRpcNetworkListen(request, sendResponse);
      return true; // 异步响应

    case 'rpcPageControl':
      handleRpcPageControl(request, sendResponse);
      return true; // 异步响应

    case 'getConfig': // 获取配置信息
      const configKey = request.configKey;
      if (configKey && configKey in config) {
        sendResponse({ url: (config as any)[configKey] });
      } else {
        sendResponse({ error: '配置项不存在' });
      }
      break;

    case 'securityVerification': // 处理安全验证通知
      // 创建桌面通知
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'static/img/icon.png',
        title: '提示',
        message: request.message || '商品不存在或者触发安全验证',
        priority: 2
      }, (notificationId: string) => {
        console.log('安全验证通知已显示，ID:', notificationId);
        // 5秒后自动关闭通知
        setTimeout(() => {
          chrome.notifications.clear(notificationId);
        }, 5000);
        sendResponse({ success: true, notificationId });
      });
      return true; // 表示异步响应

    case 'forceRemoveAllCookies':
      forceRemoveAllCookies()
        .then((result) => {
          console.log('----------------------------------------------清除Cookie成功:');
          sendResponse({ success: true });
        })
        .catch((error) => {
          console.error('-------------------------------------清除Cookie失败:', error);
          sendResponse({ success: false, error });
        });
      return true; // 表示异步响应

    case 'getLoginInfo':
      chrome.storage.sync.get(['crx_identify', 'website_id'], function(data:{crx_identify:string,website_id:string}) {
        crx_identify = data.crx_identify;
        website_id = data.website_id;
        if(crx_identify==undefined || crx_identify==''){
          responseInfo = {
            code: 0,
            page_type:0,
            opt:'login_fail',
            data: {
              crx_identify: '',
              website_id: ''
            }
          };
        }else{
          responseInfo = {
            code: 0,
            page_type:0,
            opt:'login_success_waiting_living_room',
            data: {
              crx_identify: crx_identify,
              website_id: website_id
            }
          };
        }
        sendResponse(responseInfo);
        return true;
      });
      break;

    case 'getCookie':
      chrome.tabs.query({ active: true, lastFocusedWindow: true }, async (tabs:any[]) => {
        let url = tabs[0].url;
        const cookieStr = await utils.getCookies(url);
        console.log('cookieStr==============:', cookieStr);
        responseInfo = {
          code: 0,
          opt:'get_cookie_success',
          data: {
            cookie: cookieStr
          }
        };
        sendResponse(responseInfo);
        return true;
      });
      break;
  }
  //处理异步响应
  return true
});


chrome.storage.onChanged.addListener((changes:any) => {
  for(let key in changes){
    let val = changes[key];
    if(key=='website_id'){
      console.log('website_id发生改变,当前值为'+val.newValue);
      website_id = val.newValue;
    }
    if(key=='crx_identify'){
      console.log('crx_identify发生改变,当前值为'+val.newValue);
      crx_identify = val.newValue;
    }
  }
});


chrome.storage.sync.get(['crx_identify', 'website_id'], function(data:{crx_identify:string,website_id:string}) {
  crx_identify = data.crx_identify;
  website_id = data.website_id;
});



