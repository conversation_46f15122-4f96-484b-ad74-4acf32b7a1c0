{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": false, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "allowJs": true, "checkJs": false, "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/types/**/*.d.ts"]}