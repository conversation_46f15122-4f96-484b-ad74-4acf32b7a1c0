// 页面控制器文件 - 整合DOM操作和网络监听功能
declare const chrome: any;

import type { N11RejectedProduct } from '@/utils/n11/n11RejectedApi';
import { 
  setPageSize100, 
  clickNextPage, 
  getPageStatus, 
  isN11ProductListPage,
  waitForElement,
  type PageStatus,
  type DomOperationResult 
} from './domOperations';
import { 
  createNetworkListener, 
  convertCapturedDataToN11Products,
  type NetworkListener,
  type CapturedResponseData 
} from './networkListener';

/**
 * 页面控制器配置
 */
export interface PageControllerConfig {
  tabId?: number;
  maxRetries?: number;
  pageWaitTime?: number;
  requestTimeout?: number;
}

/**
 * 数据获取进度信息
 */
export interface DataFetchProgress {
  currentPage: number;
  totalPages: number;
  processedItems: number;
  totalItems: number;
  message: string;
}

/**
 * 数据获取结果
 */
export interface DataFetchResult {
  success: boolean;
  message: string;
  data: N11RejectedProduct[];
  totalCount: number;
}

/**
 * 页面控制器类
 */
export class PageController {
  private config: Required<PageControllerConfig>;
  private tabId: number;
  private networkListener: NetworkListener | null = null;
  private isRunning: boolean = false;
  private capturedData: CapturedResponseData[] = [];

  constructor(config: PageControllerConfig = {}) {
    this.config = {
      tabId: config.tabId || 0,
      maxRetries: config.maxRetries || 3,
      pageWaitTime: config.pageWaitTime || 2000,
      requestTimeout: config.requestTimeout || 10000
    };
    this.tabId = this.config.tabId;
  }

  /**
   * 获取所有被拒绝商品数据
   */
  async getAllRejectedProducts(
    onProgress?: (current: number, total: number, message: string) => void
  ): Promise<DataFetchResult> {
    if (this.isRunning) {
      return {
        success: false,
        message: '数据获取任务已在运行中',
        data: [],
        totalCount: 0
      };
    }

    this.isRunning = true;
    this.capturedData = [];

    try {
      // 1. 获取当前活动标签页
      const tabResult = await this.getCurrentActiveTab();
      if (!tabResult.success) {
        return {
          success: false,
          message: tabResult.message,
          data: [],
          totalCount: 0
        };
      }

      this.tabId = tabResult.tabId!;
      console.log('使用标签页ID:', this.tabId);

      // 2. 验证页面环境
      const isValidPage = await isN11ProductListPage(this.tabId);
      if (!isValidPage) {
        return {
          success: false,
          message: '当前页面不是N11产品列表页面，请先导航到正确的页面',
          data: [],
          totalCount: 0
        };
      }

      if (onProgress) {
        onProgress(0, 0, '正在初始化网络监听器...');
      }

      // 3. 启动网络监听器
      this.networkListener = createNetworkListener({ tabId: this.tabId });
      const startResult = await this.networkListener.start((data) => {
        this.capturedData.push(data);
        console.log('捕获到新的响应数据');
      });

      if (!startResult.success) {
        return {
          success: false,
          message: `启动网络监听器失败: ${startResult.message}`,
          data: [],
          totalCount: 0
        };
      }

      if (onProgress) {
        onProgress(0, 0, '正在设置每页显示数量...');
      }

      // 4. 设置每页显示100条
      const pageSizeResult = await this.setPageSizeWithRetry();
      if (!pageSizeResult.success) {
        console.warn('设置页面大小失败，继续使用默认设置:', pageSizeResult.message);
      }

      // 等待页面更新
      await this.delay(this.config.pageWaitTime);

      if (onProgress) {
        onProgress(0, 0, '正在获取页面信息...');
      }

      // 5. 获取页面状态信息
      const pageStatus = await getPageStatus(this.tabId);
      if (!pageStatus) {
        return {
          success: false,
          message: '无法获取页面状态信息',
          data: [],
          totalCount: 0
        };
      }

      console.log('页面状态:', pageStatus);

      // 6. 等待第一页数据加载
      await this.waitForPageData(1);

      // 7. 遍历所有分页
      const fetchResult = await this.fetchAllPages(pageStatus, onProgress);
      
      // 8. 转换数据格式
      const allProducts = convertCapturedDataToN11Products(this.capturedData);

      return {
        success: fetchResult.success,
        message: fetchResult.message,
        data: allProducts,
        totalCount: allProducts.length
      };

    } catch (error: any) {
      console.error('获取数据过程中发生错误:', error);
      return {
        success: false,
        message: `获取数据失败: ${error.message}`,
        data: [],
        totalCount: 0
      };
    } finally {
      await this.cleanup();
      this.isRunning = false;
    }
  }

  /**
   * 获取当前活动标签页
   */
  private async getCurrentActiveTab(): Promise<{ success: boolean; message: string; tabId?: number }> {
    return new Promise((resolve) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs: any[]) => {
        if (chrome.runtime.lastError) {
          resolve({
            success: false,
            message: `获取活动标签页失败: ${chrome.runtime.lastError.message}`
          });
          return;
        }

        if (!tabs || tabs.length === 0) {
          resolve({
            success: false,
            message: '未找到活动标签页'
          });
          return;
        }

        const activeTab = tabs[0];
        resolve({
          success: true,
          message: '成功获取活动标签页',
          tabId: activeTab.id
        });
      });
    });
  }

  /**
   * 设置页面大小（带重试）
   */
  private async setPageSizeWithRetry(): Promise<DomOperationResult> {
    for (let i = 0; i < this.config.maxRetries; i++) {
      try {
        const result = await setPageSize100(this.tabId);
        if (result.success) {
          return result;
        }
        
        console.log(`第 ${i + 1} 次设置页面大小失败:`, result.message);
        
        if (i < this.config.maxRetries - 1) {
          await this.delay(1000);
        }
      } catch (error: any) {
        console.error(`第 ${i + 1} 次设置页面大小异常:`, error);
        if (i === this.config.maxRetries - 1) {
          return { success: false, message: `设置失败: ${error.message}` };
        }
      }
    }

    return { success: false, message: '设置页面大小失败，已达到最大重试次数' };
  }

  /**
   * 等待页面数据加载
   */
  private async waitForPageData(expectedPage: number): Promise<boolean> {
    const maxWaitTime = 15000; // 最大等待15秒
    const checkInterval = 500;
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      // 检查是否有新的响应数据
      if (this.capturedData.length > 0) {
        const latestData = this.capturedData[this.capturedData.length - 1];
        if (latestData.timestamp > startTime) {
          console.log(`第 ${expectedPage} 页数据已加载`);
          return true;
        }
      }

      await this.delay(checkInterval);
    }

    console.warn(`等待第 ${expectedPage} 页数据超时`);
    return false;
  }

  /**
   * 获取所有分页数据
   */
  private async fetchAllPages(
    initialPageStatus: PageStatus,
    onProgress?: (current: number, total: number, message: string) => void
  ): Promise<{ success: boolean; message: string }> {
    try {
      const totalPages = initialPageStatus.totalPages;
      let currentPage = initialPageStatus.currentPage;

      console.log(`开始获取 ${totalPages} 页数据，当前页: ${currentPage}`);

      // 报告初始进度
      if (onProgress) {
        onProgress(currentPage, totalPages, `正在处理第 ${currentPage} 页...`);
      }

      // 如果总页数大于1，需要遍历其他页面
      for (let page = currentPage + 1; page <= totalPages; page++) {
        if (onProgress) {
          onProgress(page - 1, totalPages, `正在切换到第 ${page} 页...`);
        }

        // 点击下一页
        const clickResult = await this.clickNextPageWithRetry();
        if (!clickResult.success) {
          console.error(`切换到第 ${page} 页失败:`, clickResult.message);
          // 继续处理下一页，不中断整个流程
          continue;
        }

        // 等待页面加载
        await this.delay(this.config.pageWaitTime);

        // 等待数据加载
        const dataLoaded = await this.waitForPageData(page);
        if (!dataLoaded) {
          console.warn(`第 ${page} 页数据加载超时`);
        }

        if (onProgress) {
          onProgress(page, totalPages, `已处理第 ${page} 页`);
        }

        console.log(`第 ${page} 页处理完成`);
      }

      return {
        success: true,
        message: `成功获取 ${totalPages} 页数据`
      };

    } catch (error: any) {
      console.error('获取分页数据失败:', error);
      return {
        success: false,
        message: `获取分页数据失败: ${error.message}`
      };
    }
  }

  /**
   * 点击下一页（带重试）
   */
  private async clickNextPageWithRetry(): Promise<DomOperationResult> {
    for (let i = 0; i < this.config.maxRetries; i++) {
      try {
        const result = await clickNextPage(this.tabId);
        if (result.success) {
          return result;
        }
        
        console.log(`第 ${i + 1} 次点击下一页失败:`, result.message);
        
        if (i < this.config.maxRetries - 1) {
          await this.delay(1000);
        }
      } catch (error: any) {
        console.error(`第 ${i + 1} 次点击下一页异常:`, error);
        if (i === this.config.maxRetries - 1) {
          return { success: false, message: `点击失败: ${error.message}` };
        }
      }
    }

    return { success: false, message: '点击下一页失败，已达到最大重试次数' };
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清理资源
   */
  private async cleanup(): Promise<void> {
    try {
      if (this.networkListener) {
        await this.networkListener.stop();
        this.networkListener = null;
      }
      console.log('页面控制器资源已清理');
    } catch (error) {
      console.error('清理资源时发生错误:', error);
    }
  }

  /**
   * 停止数据获取
   */
  async stop(): Promise<void> {
    this.isRunning = false;
    await this.cleanup();
  }
}

/**
 * 创建页面控制器实例
 */
export function createPageController(config?: PageControllerConfig): PageController {
  return new PageController(config);
}

/**
 * 通过RPC方式获取所有被拒绝商品（便捷函数）
 */
export async function getAllRejectedProductsViaRPC(
  onProgress?: (current: number, total: number) => void
): Promise<N11RejectedProduct[]> {
  const controller = createPageController();
  
  const result = await controller.getAllRejectedProducts((current, total, message) => {
    console.log(`进度: ${current}/${total} - ${message}`);
    if (onProgress) {
      onProgress(current, total);
    }
  });

  if (!result.success) {
    throw new Error(result.message);
  }

  return result.data;
} 