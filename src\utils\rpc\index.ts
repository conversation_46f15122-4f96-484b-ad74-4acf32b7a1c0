// RPC模块统一导出文件
declare const chrome: any;

export * from './domOperations';
export * from './networkListener';
export * from './pageController';

// 主要功能导出
export { getAllRejectedProductsViaRPC } from './pageController';

// 类型定义导出
export type {
  DomOperationResult,
  PageStatus,
  WaitElementConfig
} from './domOperations';

export type {
  NetworkListenerConfig,
  NetworkListenerResult,
  CapturedRequestData,
  CapturedResponseData
} from './networkListener';

export type {
  PageControllerConfig,
  DataFetchProgress,
  DataFetchResult
} from './pageController';

// 配置选项
export interface RPCConfig {
  // DOM操作配置
  maxRetries?: number;          // 最大重试次数，默认3
  pageWaitTime?: number;        // 页面切换等待时间，默认2000ms
  requestTimeout?: number;      // 请求超时时间，默认10000ms
  
  // 网络监听配置
  networkTimeout?: number;      // 网络监听超时时间，默认15000ms
  
  // 页面检测配置
  elementWaitTimeout?: number;  // 元素等待超时时间，默认10000ms
  elementCheckInterval?: number; // 元素检查间隔，默认500ms
}

// 默认配置
export const DEFAULT_RPC_CONFIG: Required<RPCConfig> = {
  maxRetries: 3,
  pageWaitTime: 2000,
  requestTimeout: 10000,
  networkTimeout: 15000,
  elementWaitTimeout: 10000,
  elementCheckInterval: 500
};

// 错误类型定义
export class RPCError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'RPCError';
  }
}

export class NetworkListenerError extends RPCError {
  constructor(message: string, details?: any) {
    super(message, 'NETWORK_LISTENER_ERROR', details);
    this.name = 'NetworkListenerError';
  }
}

export class DOMOperationError extends RPCError {
  constructor(message: string, details?: any) {
    super(message, 'DOM_OPERATION_ERROR', details);
    this.name = 'DOMOperationError';
  }
}

export class PageValidationError extends RPCError {
  constructor(message: string, details?: any) {
    super(message, 'PAGE_VALIDATION_ERROR', details);
    this.name = 'PageValidationError';
  }
}

// 工具函数
/**
 * 检查当前环境是否支持RPC操作
 */
export function isRPCSupported(): boolean {
  try {
    // 检查Chrome扩展环境
    if (typeof chrome === 'undefined' || !chrome.runtime) {
      return false;
    }

    // 检查必要的API
    const requiredAPIs = [
      'chrome.scripting',
      'chrome.debugger',
      'chrome.tabs'
    ];

    for (const api of requiredAPIs) {
      const apiPath = api.split('.');
      let obj = chrome;
      for (const path of apiPath.slice(1)) {
        if (!obj || typeof obj[path] === 'undefined') {
          console.warn(`缺少必要的API: ${api}`);
          return false;
        }
        obj = obj[path];
      }
    }

    return true;
  } catch (error) {
    console.error('检查RPC支持时发生错误:', error);
    return false;
  }
}



/**
 * 检查页面是否为N11产品列表页面（便捷函数）
 */
export async function checkN11ProductListPage(): Promise<boolean> {
  try {
    // 基础环境检查
    if (typeof chrome === 'undefined' || !chrome.runtime || !chrome.tabs || !chrome.scripting) {
      console.log('Chrome API环境不完整');
      return false;
    }

    // 获取当前活动标签页
    const tabs = await new Promise<any[]>((resolve, reject) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (result: any[]) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(result);
        }
      });
    });

    if (!tabs || tabs.length === 0) {
      console.log('未找到活动标签页');
      return false;
    }

    const tabId = tabs[0].id;
    const url = tabs[0].url;
    
    // 首先通过URL进行基础检查
    if (!url || !url.includes('so.n11.com')) {
      console.log('当前页面不是N11网站');
      return false;
    }

    // 通过chrome.scripting检查DOM结构
    try {
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: () => {
          try {
            const url = window.location.href;
            const hasCorrectUrl = url.includes('so.n11.com') && 
                                 (url.includes('product') || url.includes('urun'));
            
            const hasProductList = document.querySelector('.product-list') ||
                                  document.querySelector('[class*="product"]') ||
                                  document.querySelector('.tabManager');
            
            return hasCorrectUrl && !!hasProductList;
          } catch (error) {
            return false;
          }
        }
      });
      
      return !!(results && results[0] && results[0].result);
    } catch (scriptError) {
      console.error('执行页面检查脚本失败:', scriptError);
      // 如果脚本执行失败，回退到URL检查
      return url.includes('so.n11.com') && (url.includes('product') || url.includes('urun'));
    }
  } catch (error) {
    console.error('检查N11页面时发生错误:', error);
    return false;
  }
}

/**
 * 获取RPC功能状态信息
 */
export interface RPCStatus {
  supported: boolean;
  chromeAPIs: {
    scripting: boolean;
    debugger: boolean;
    tabs: boolean;
  };
  currentPage: {
    isN11: boolean;
    url?: string;
    tabId?: number;
  };
  permissions: {
    debugger: boolean;
    scripting: boolean;
    activeTab: boolean;
  };
}

/**
 * 获取RPC功能详细状态 - 安全版本（避免在content script中直接使用Chrome API）
 */
export async function getRPCStatus(): Promise<RPCStatus & { message?: string; error?: string }> {
  const status: RPCStatus & { message?: string; error?: string } = {
    supported: false,
    chromeAPIs: {
      scripting: false,
      debugger: false,
      tabs: false
    },
    currentPage: {
      isN11: false
    },
    permissions: {
      debugger: false,
      scripting: false,
      activeTab: false
    }
  };

  try {
    console.log('开始检查RPC状态...');
    
    // 检查基本Chrome环境
    if (typeof chrome === 'undefined') {
      status.message = 'Chrome对象未定义';
      status.error = 'Chrome extension environment not detected';
      console.error('Chrome环境不可用：chrome对象未定义');
      return status;
    }

    if (!chrome.runtime) {
      status.message = 'chrome.runtime不可用';
      status.error = 'chrome.runtime API not available';
      console.error('Chrome运行时环境不可用');
      return status;
    }

    console.log('Chrome运行时环境检查通过');

    // 检测运行环境类型
    const isBackgroundContext = !!(chrome.tabs && chrome.scripting && chrome.debugger);
    const isContentScriptContext = typeof window !== 'undefined' && typeof document !== 'undefined';
    
    console.log('环境检测结果:', { isBackgroundContext, isContentScriptContext });

    if (isBackgroundContext) {
      // 在background环境中，可以直接检查API
      console.log('在Background环境中执行RPC状态检查');
      
      status.chromeAPIs.scripting = !!(chrome.scripting && chrome.scripting.executeScript);
      status.chromeAPIs.debugger = !!(chrome.debugger && chrome.debugger.attach);
      status.chromeAPIs.tabs = !!(chrome.tabs && chrome.tabs.query);
      
      console.log('Chrome API检查结果:', status.chromeAPIs);

      // 检查权限
      if (chrome.permissions) {
        try {
          const permissions = await new Promise<any>((resolve, reject) => {
            chrome.permissions.getAll((result: any) => {
              if (chrome.runtime.lastError) {
                reject(chrome.runtime.lastError);
              } else {
                resolve(result);
              }
            });
          });

          status.permissions.debugger = permissions.permissions?.includes('debugger') || false;
          status.permissions.scripting = permissions.permissions?.includes('scripting') || false;
          status.permissions.activeTab = permissions.permissions?.includes('activeTab') || false;
          
          console.log('权限检查结果:', status.permissions);
        } catch (permError) {
          console.error('权限检查失败:', permError);
          status.error = `权限检查失败: ${permError}`;
        }
      }

      // 检查当前页面
      if (chrome.tabs) {
        try {
          const tabs = await new Promise<any[]>((resolve, reject) => {
            chrome.tabs.query({ active: true, currentWindow: true }, (result: any[]) => {
              if (chrome.runtime.lastError) {
                reject(chrome.runtime.lastError);
              } else {
                resolve(result);
              }
            });
          });

          if (tabs && tabs.length > 0) {
            const activeTab = tabs[0];
            status.currentPage.url = activeTab.url;
            status.currentPage.tabId = activeTab.id;
            
            if (activeTab.url) {
              status.currentPage.isN11 = activeTab.url.includes('so.n11.com') && 
                                       (activeTab.url.includes('product') || activeTab.url.includes('urun'));
            }
          }
        } catch (tabError) {
          console.error('获取当前页面信息失败:', tabError);
          status.error = `获取页面信息失败: ${tabError}`;
        }
      }

    } else if (isContentScriptContext) {
      // 在content script环境中，使用消息传递方式获取状态
      console.log('在Content Script环境中，通过消息传递获取RPC状态');
      
      try {
        const result = await new Promise<any>((resolve, reject) => {
          chrome.runtime.sendMessage({
            funType: 'rpcPageControl',
            action: 'getRPCStatus'
          }, (response: any) => {
            if (chrome.runtime.lastError) {
              reject(chrome.runtime.lastError);
            } else {
              resolve(response);
            }
          });
        });

        if (result) {
          Object.assign(status, result);
          console.log('从Background获取的RPC状态:', result);
        } else {
          status.message = '未能从Background获取RPC状态';
        }
      } catch (msgError) {
        console.error('消息传递获取RPC状态失败:', msgError);
        status.message = '消息传递失败';
        status.error = `消息传递错误: ${msgError}`;
      }

      // 在content script中也可以检查当前页面URL
      if (typeof window !== 'undefined' && window.location) {
        const url = window.location.href;
        status.currentPage.url = url;
        status.currentPage.isN11 = url.includes('so.n11.com') && 
                                 (url.includes('product') || url.includes('urun'));
        console.log('Content Script页面检查结果:', status.currentPage.isN11);
      }

    } else {
      // 其他环境（如popup）
      console.log('在其他环境中，尝试通过消息传递获取RPC状态');
      
      status.message = '当前环境不支持直接RPC操作';
      status.error = '需要在Background或Content Script环境中执行';
    }

    // 综合判断是否支持
    const apiSupported = status.chromeAPIs.scripting && 
                        status.chromeAPIs.debugger && 
                        status.chromeAPIs.tabs;
    
    const permissionsGranted = status.permissions.debugger &&
                              status.permissions.scripting;
    
    status.supported = apiSupported && permissionsGranted;
    
    console.log('最终RPC支持状态:', status.supported);
    
    if (!status.supported && !status.message) {
      const reasons = [];
      if (!apiSupported) reasons.push('Chrome API不完整');
      if (!permissionsGranted) reasons.push('权限不足');
      status.message = reasons.join(', ');
    }

    return status;
  } catch (error: any) {
    console.error('获取RPC状态时发生错误:', error);
    status.message = '状态检查失败';
    status.error = error.message || '未知错误';
    return status;
  }
} 