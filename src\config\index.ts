import { ta } from "element-plus/es/locale";

const apiUrls = {
  production: {
    loginUrl    : 'https://trade.dailyhotnews.com.cn/api/login/loginSms',
    registerUrl : 'https://trade.dailyhotnews.com.cn/api/user/register',
    captcha     : 'https://trade.dailyhotnews.com.cn/api/captcha',
    temuGoodsAddUrl : 'https://trade.dailyhotnews.com.cn/api/temu/goodsAdd',
    userInfoUrl : 'https://trade.dailyhotnews.com.cn/api/user/info',
    userLogoutUrl : 'https://trade.dailyhotnews.com.cn/api/user/logout',
    storeListUrl : 'https://trade.dailyhotnews.com.cn/api/store/list',
    storeCreateUrl : 'https://trade.dailyhotnews.com.cn/api/store/create',
    storeUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/store/update',
    storeDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/store/delete',
    storeDetailUrl : 'https://trade.dailyhotnews.com.cn/api/store/detail',
    storeBatchUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/store/batch-update',
    goodsListUrl : 'https://trade.dailyhotnews.com.cn/api/goods/list',
    goodsAddUrl : 'https://trade.dailyhotnews.com.cn/api/goods/add',
    goodsUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods/update',
    goodsDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/goods/delete',
    goodsDetailUrl : 'https://trade.dailyhotnews.com.cn/api/goods/detail',
    goodsBatchUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods/batch-update',
    catTemuWebpageMainUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_webpage_main',
    catTemuWebpageListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_webpage_list',
    catTemuMainUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_main',
    catTemuListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_list',
    catTemuListLazyUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_list_lazy',
    catTemuListflatUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_list_flat',
    catTemuChildrenCountUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_children_count',
    catTemuChildrenCountsUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_children_counts',
    catN11ListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_n11_list',
    catN11DetailUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_n11_detail',
    catN11UpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_n11_update',
    catRelationSetUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_relation_set',
    catRelationListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_relation_list',
    userStoreInfoUrl : 'https://trade.dailyhotnews.com.cn/api/user/store_info',
    taskAddUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/add',
    taskListUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/list',
    taskStartUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/start',
    taskUpdateUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/update',
    taskDetailUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail',
    taskDetailListUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail-list',
    taskQueryResultsUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/query-results',
    taskPendingQueryUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/pending-query',
    taskBatchUpdateUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/batch-update',
    taskSaveUploadParamsUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/save-upload-params',
    taskRetryUploadParamsUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/retry-upload-params',
    taskBatchRetryUploadUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/batch-retry-upload',
    catTemuDetailUrl: 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_detail',
    goodsDirectoryListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/list',
    goodsDirectoryCreateUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/create',
    goodsDirectoryUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/update',
    goodsDirectoryDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/delete',
    goodsDirectoryDetailUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/detail',
    goodsDirectoryBatchUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/batch-update',
    goodsDirectoryUpdateGoodsCountUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/update-goods-count',
    goodsDirectoryUpdateAllGoodsCountUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/update-all-goods-count',
    userCollectionSettingsUrl : 'https://trade.dailyhotnews.com.cn/api/user/collection-settings',
    userAvailableDirectoriesUrl : 'https://trade.dailyhotnews.com.cn/api/user/available-directories',
    userCheckNeedSetupUrl : 'https://trade.dailyhotnews.com.cn/api/user/check-need-setup',
    userProcessGoodsImagesUrl : 'https://trade.dailyhotnews.com.cn/api/user/process-goods-images',
    goodsNeedImageProcessUrl : 'https://trade.dailyhotnews.com.cn/api/goods/need-image-process',
    goodsStatisticsUrl : 'https://trade.dailyhotnews.com.cn/api/goods/statistics',
    kfUrl : 'https://trade.dailyhotnews.com.cn/qrcode/kf.png',
    n11RejectedProductBatchSaveUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/batch-save',
    n11RejectedProductPendingCountUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/pending-count',
    n11RejectedProductNextPendingUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/next-pending',
    n11RejectedProductUpdateStatusUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/update-status',
    n11RejectedProductStatisticsUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/statistics',
  },
  development_1: {
    loginUrl    : 'https://trade.dailyhotnews.com.cn/api/login/loginSms',
    registerUrl : 'https://trade.dailyhotnews.com.cn/api/user/register',
    captcha     : 'https://trade.dailyhotnews.com.cn/api/captcha',
    temuGoodsAddUrl : 'https://trade.dailyhotnews.com.cn/api/temu/goodsAdd',
    userInfoUrl : 'https://trade.dailyhotnews.com.cn/api/user/info',
    userLogoutUrl : 'https://trade.dailyhotnews.com.cn/api/user/logout',
    storeListUrl : 'https://trade.dailyhotnews.com.cn/api/store/list',
    storeCreateUrl : 'https://trade.dailyhotnews.com.cn/api/store/create',
    storeUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/store/update',
    storeDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/store/delete',
    storeDetailUrl : 'https://trade.dailyhotnews.com.cn/api/store/detail',
    storeBatchUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/store/batch-update',
    goodsListUrl : 'https://trade.dailyhotnews.com.cn/api/goods/list',
    goodsAddUrl : 'https://trade.dailyhotnews.com.cn/api/goods/add',
    goodsUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods/update',
    goodsDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/goods/delete',
    goodsDetailUrl : 'https://trade.dailyhotnews.com.cn/api/goods/detail',
    goodsBatchUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods/batch-update',
    catTemuWebpageMainUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_webpage_main',
    catTemuWebpageListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_webpage_list',
    catTemuMainUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_main',
    catTemuListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_list',
    catTemuListLazyUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_list_lazy',
    catTemuListflatUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_list_flat',
    catTemuChildrenCountUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_children_count',
    catTemuChildrenCountsUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_children_counts',
    catN11ListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_n11_list',
    catN11DetailUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_n11_detail',
    catN11UpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_n11_update',
    catRelationSetUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_relation_set',
    catRelationListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_relation_list',
    userStoreInfoUrl : 'https://trade.dailyhotnews.com.cn/api/user/store_info',
    taskAddUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/add',
    taskListUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/list',
    taskStartUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/start',
    taskUpdateUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/update',
    taskDetailUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail',
    taskDetailListUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail-list',
    taskQueryResultsUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/query-results',
    taskPendingQueryUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/pending-query',
    taskBatchUpdateUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/batch-update',
    taskSaveUploadParamsUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/save-upload-params',
    taskRetryUploadParamsUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/retry-upload-params',
    taskBatchRetryUploadUrl: 'https://trade.dailyhotnews.com.cn/api/user/task/detail/batch-retry-upload',
    catTemuDetailUrl: 'https://trade.dailyhotnews.com.cn/api/goods_category/cat_temu_detail',
    goodsDirectoryListUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/list',
    goodsDirectoryCreateUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/create',
    goodsDirectoryUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/update',
    goodsDirectoryDeleteUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/delete',
    goodsDirectoryDetailUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/detail',
    goodsDirectoryBatchUpdateUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/batch-update',
    goodsDirectoryUpdateGoodsCountUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/update-goods-count',
    goodsDirectoryUpdateAllGoodsCountUrl : 'https://trade.dailyhotnews.com.cn/api/goods_directory/update-all-goods-count',
    userCollectionSettingsUrl : 'https://trade.dailyhotnews.com.cn/api/user/collection-settings',
    userAvailableDirectoriesUrl : 'https://trade.dailyhotnews.com.cn/api/user/available-directories',
    userCheckNeedSetupUrl : 'https://trade.dailyhotnews.com.cn/api/user/check-need-setup',
    userProcessGoodsImagesUrl : 'https://trade.dailyhotnews.com.cn/api/user/process-goods-images',
    goodsNeedImageProcessUrl : 'https://trade.dailyhotnews.com.cn/api/goods/need-image-process',
    goodsStatisticsUrl : 'https://trade.dailyhotnews.com.cn/api/goods/statistics',
    kfUrl : 'https://trade.dailyhotnews.com.cn/qrcode/kf.png',
    n11RejectedProductBatchSaveUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/batch-save',
    n11RejectedProductPendingCountUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/pending-count',
    n11RejectedProductNextPendingUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/next-pending',
    n11RejectedProductUpdateStatusUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/update-status',
    n11RejectedProductStatisticsUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/statistics',
  },
  development: {
    loginUrl    : 'http://tsa.test.com/api/login/loginSms',
    registerUrl : 'http://tsa.test.com/api/user/register',
    captcha     : 'http://tsa.test.com/api/captcha',
    temuGoodsAddUrl : 'http://tsa.test.com/api/temu/goodsAdd',
    userInfoUrl : 'http://tsa.test.com/api/user/info',
    userLogoutUrl : 'http://tsa.test.com/api/user/logout',
    storeListUrl : 'http://tsa.test.com/api/store/list',
    storeCreateUrl : 'http://tsa.test.com/api/store/create',
    storeUpdateUrl : 'http://tsa.test.com/api/store/update',
    storeDeleteUrl : 'http://tsa.test.com/api/store/delete',
    storeDetailUrl : 'http://tsa.test.com/api/store/detail',
    storeBatchUpdateUrl : 'http://tsa.test.com/api/store/batch-update',
    goodsListUrl : 'http://tsa.test.com/api/goods/list',
    goodsAddUrl : 'http://tsa.test.com/api/goods/add',
    goodsUpdateUrl : 'http://tsa.test.com/api/goods/update',
    goodsDeleteUrl : 'http://tsa.test.com/api/goods/delete',
    goodsDetailUrl : 'http://tsa.test.com/api/goods/detail',
    goodsBatchUpdateUrl : 'http://tsa.test.com/api/goods/batch-update',
    catTemuWebpageMainUrl : 'http://tsa.test.com/api/goods_category/cat_temu_webpage_main',
    catTemuWebpageListUrl : 'http://tsa.test.com/api/goods_category/cat_temu_webpage_list',
    catTemuMainUrl : 'http://tsa.test.com/api/goods_category/cat_temu_main',
    catTemuListUrl : 'http://tsa.test.com/api/goods_category/cat_temu_list',
    catTemuListLazyUrl : 'http://tsa.test.com/api/goods_category/cat_temu_list_lazy',
    catTemuListflatUrl : 'http://tsa.test.com/api/goods_category/cat_temu_list_flat',
    catTemuChildrenCountUrl : 'http://tsa.test.com/api/goods_category/cat_temu_children_count',
    catTemuChildrenCountsUrl : 'http://tsa.test.com/api/goods_category/cat_temu_children_counts',
    catN11ListUrl : 'http://tsa.test.com/api/goods_category/cat_n11_list',
    catN11DetailUrl : 'http://tsa.test.com/api/goods_category/cat_n11_detail',
    catN11UpdateUrl : 'http://tsa.test.com/api/goods_category/cat_n11_update',
    catRelationSetUrl : 'http://tsa.test.com/api/goods_category/cat_relation_set',
    catRelationListUrl : 'http://tsa.test.com/api/goods_category/cat_relation_list',
    userStoreInfoUrl : 'http://tsa.test.com/api/user/store_info',
    taskAddUrl: 'http://tsa.test.com/api/user/task/add',
    taskListUrl: 'http://tsa.test.com/api/user/task/list',
    taskStartUrl: 'http://tsa.test.com/api/user/task/start',
    taskUpdateUrl: 'http://tsa.test.com/api/user/task/update',
    taskDetailUrl: 'http://tsa.test.com/api/user/task/detail',
    taskDetailListUrl: 'http://tsa.test.com/api/user/task/detail-list',
    taskQueryResultsUrl: 'http://tsa.test.com/api/user/task/detail/query-results',
    taskPendingQueryUrl: 'http://tsa.test.com/api/user/task/detail/pending-query',
    taskBatchUpdateUrl: 'http://tsa.test.com/api/user/task/detail/batch-update',
    taskSaveUploadParamsUrl: 'http://tsa.test.com/api/user/task/detail/save-upload-params',
    taskRetryUploadParamsUrl: 'http://tsa.test.com/api/user/task/detail/retry-upload-params',
    taskBatchRetryUploadUrl: 'http://tsa.test.com/api/user/task/detail/batch-retry-upload',
    catTemuDetailUrl: 'http://tsa.test.com/api/goods_category/cat_temu_detail',
    goodsDirectoryListUrl : 'http://tsa.test.com/api/goods_directory/list',
    goodsDirectoryCreateUrl : 'http://tsa.test.com/api/goods_directory/create',
    goodsDirectoryUpdateUrl : 'http://tsa.test.com/api/goods_directory/update',
    goodsDirectoryDeleteUrl : 'http://tsa.test.com/api/goods_directory/delete',
    goodsDirectoryDetailUrl : 'http://tsa.test.com/api/goods_directory/detail',
    goodsDirectoryBatchUpdateUrl : 'http://tsa.test.com/api/goods_directory/batch-update',
    goodsDirectoryUpdateGoodsCountUrl : 'http://tsa.test.com/api/goods_directory/update-goods-count',
    goodsDirectoryUpdateAllGoodsCountUrl : 'http://tsa.test.com/api/goods_directory/update-all-goods-count',
    userCollectionSettingsUrl : 'http://tsa.test.com/api/user/collection-settings',
    userAvailableDirectoriesUrl : 'http://tsa.test.com/api/user/available-directories',
    userCheckNeedSetupUrl : 'http://tsa.test.com/api/user/check-need-setup',
    userProcessGoodsImagesUrl : 'http://tsa.test.com/api/user/process-goods-images',
    goodsNeedImageProcessUrl : 'http://tsa.test.com/api/goods/need-image-process',
    goodsStatisticsUrl : 'http://tsa.test.com/api/goods/statistics',
    kfUrl : 'http://tsa.test.com/qrcode/kf.png',
    n11RejectedProductBatchSaveUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/batch-save',
    n11RejectedProductPendingCountUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/pending-count',
    n11RejectedProductNextPendingUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/next-pending',
    n11RejectedProductUpdateStatusUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/update-status',
    n11RejectedProductStatisticsUrl: 'https://trade.dailyhotnews.com.cn/api/n11/rejected-products/statistics',
  }
};

export default {
  ay :'hU5IfoZGq43TPkWr',
  api: '*',
  // N11调试模式开关 - 前端配置，true为调试模式（不真正调用N11 API），false为正常模式
  n11DebugMode: false,
  apiLoginUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.loginUrl,
  apiRegisterUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.registerUrl,
  apiCaptchaUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.captcha,
  apiTemuGoodsAddUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.temuGoodsAddUrl,
  apiUserInfoUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.userInfoUrl,
  apiUserLogoutUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.userLogoutUrl,
  apiStoreListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.storeListUrl,
  apiStoreCreateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.storeCreateUrl,
  apiStoreUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.storeUpdateUrl,
  apiStoreDeleteUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.storeDeleteUrl,
  apiStoreDetailUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.storeDetailUrl,
  apiStoreBatchUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.storeBatchUpdateUrl,
  apiGoodsListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsListUrl,
  apiGoodsAddUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsAddUrl,
  apiGoodsUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsUpdateUrl,
  apiGoodsDeleteUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDeleteUrl,
  apiGoodsDetailUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDetailUrl,
  apiGoodsBatchUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsBatchUpdateUrl,
  apiCatTemuWebpageMainUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuWebpageMainUrl,
  apiCatTemuWebpageListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuWebpageListUrl,
  apiCatTemuMainUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuMainUrl,
  apiCatTemuListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuListUrl,
  apiCatTemuListLazyUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuListLazyUrl,
  apiCatTemuListflatUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuListflatUrl,
  apiCatTemuChildrenCountUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuChildrenCountUrl,
  apiCatTemuChildrenCountsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuChildrenCountsUrl,
  apiCatN11ListUrl:apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catN11ListUrl,
  apiCatN11DetailUrl:apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catN11DetailUrl,
  apiCatN11UpdateUrl:apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catN11UpdateUrl,
  apiCatRelationSetUrl:apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catRelationSetUrl,
  apiCatRelationListUrl:apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catRelationListUrl,
  apiUserStoreInfoUrl:apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.userStoreInfoUrl,
  apiTaskAddUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskAddUrl,
  apiTaskListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskListUrl,
  apiTaskStartUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskStartUrl,
  apiTaskUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskUpdateUrl,
  apiTaskDetailUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskDetailUrl,
  apiTaskDetailListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskDetailListUrl,
  apiTaskQueryResultsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskQueryResultsUrl,
  apiTaskPendingQueryUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskPendingQueryUrl,
  apiTaskBatchUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskBatchUpdateUrl,
  apiTaskSaveUploadParamsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskSaveUploadParamsUrl,
  apiTaskRetryUploadParamsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskRetryUploadParamsUrl,
  apiTaskBatchRetryUploadUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.taskBatchRetryUploadUrl,
  apiCatTemuDetailUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.catTemuDetailUrl,
  apiGoodsDirectoryListUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDirectoryListUrl,
  apiGoodsDirectoryCreateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDirectoryCreateUrl,
  apiGoodsDirectoryUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDirectoryUpdateUrl,
  apiGoodsDirectoryDeleteUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDirectoryDeleteUrl,
  apiGoodsDirectoryDetailUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDirectoryDetailUrl,
  apiGoodsDirectoryBatchUpdateUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDirectoryBatchUpdateUrl,
  apiGoodsDirectoryUpdateGoodsCountUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDirectoryUpdateGoodsCountUrl,
  apiGoodsDirectoryUpdateAllGoodsCountUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsDirectoryUpdateAllGoodsCountUrl,
  apiUserCollectionSettingsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.userCollectionSettingsUrl,
  apiUserAvailableDirectoriesUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.userAvailableDirectoriesUrl,
  apiUserCheckNeedSetupUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.userCheckNeedSetupUrl,
  apiUserProcessGoodsImagesUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.userProcessGoodsImagesUrl,
  apiGoodsNeedImageProcessUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsNeedImageProcessUrl,
  apiGoodsStatisticsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.goodsStatisticsUrl,
  apiKfUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.kfUrl,
  // N11被拒绝商品相关API
  n11RejectedProductBatchSaveUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.n11RejectedProductBatchSaveUrl,
  n11RejectedProductPendingCountUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.n11RejectedProductPendingCountUrl,
  n11RejectedProductNextPendingUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.n11RejectedProductNextPendingUrl,
  n11RejectedProductUpdateStatusUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.n11RejectedProductUpdateStatusUrl,
  n11RejectedProductStatisticsUrl: apiUrls[process.env.NODE_ENV as keyof typeof apiUrls]?.n11RejectedProductStatisticsUrl,
  urlPage:{
    'living_room_index' : 'https://leads.cluerich.com/pc/analysis/live-screen?fullscreen=0',
  },
};
