# N11商品分类名称更新命令使用说明

## 功能描述
该命令用于从Excel文件读取数据，更新N11商品分类表中的中文名称（name字段）。

## 使用步骤

### 1. 准备Excel文件
- 将Excel文件命名为 `n11.xlsx`
- 放置在 `storage/excel/` 目录下
- Excel文件格式要求：
  - 第一行为标题行（会被跳过）
  - A列：分类ID（数字）
  - C列：中文名称

### 2. 执行命令
```bash
php artisan n11:update-category-names
```

### 3. 命令执行流程
1. **读取Excel文件** - 自动读取并验证文件格式
2. **统计数据** - 显示Excel记录数和MySQL记录数
3. **确认执行** - 需要手动确认是否执行更新操作
4. **执行更新** - 显示详细的进度条和当前处理信息
5. **结果统计** - 显示成功/失败数量
6. **保存失败记录** - 失败的ID会保存到日志文件中

### 4. 进度显示
执行过程中会显示：
- 当前更新的ID和名称
- 进行到第几个记录
- 还剩多少个记录未处理
- 实时进度条

### 5. 结果文件
如果有更新失败的记录，会在 `storage/logs/` 目录下生成失败记录文件：
- 文件名格式：`n11_category_update_failed_YYYYMMDDHHMMSS.txt`
- 包含失败的ID列表，方便后续核实

## 注意事项
- 确保Excel文件格式正确
- 命令执行前会有确认提示，可以取消操作
- 更新操作是基于ID匹配，只更新存在的记录
- 失败记录会被详细记录，便于问题排查

## 数据库字段说明
更新的是 `tsa_product_category_n11` 表的 `name` 字段（分类名称字段用于存储中文名称）。 